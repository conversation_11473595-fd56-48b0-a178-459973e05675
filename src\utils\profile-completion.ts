/**
 * Profile completion calculation utilities
 * Based on the profile-completion-docs.md specification
 */

import {
  ProfileCompletionData,
  ProfileCompletionResult,
  CompletionBreakdown,
  SectionCompletion,
  ProviderInfoCompletion,
  LocationCompletion,
  ServiceCompletion,
  QueueCompletion,
  DEFAULT_COMPLETION_WEIGHTS,
  COMPLETION_THRESHOLDS,
} from '../types/profile-completion';

/**
 * Main function to calculate profile completion
 */
export function calculateProfileCompletion(data: ProfileCompletionData): ProfileCompletionResult {
  const { user, provider } = data;

  // Safety guard for null/undefined data
  if (!user || !provider) {
    return createEmptyCompletionResult();
  }

  // Calculate each section
  const profilePictureResult = checkProfilePicture(provider);
  const providerInfoResult = checkProviderInfo(provider);
  const providingPlacesResult = checkProvidingPlaces(provider.providingPlaces || []);
  const servicesResult = checkServices(provider.services || []);
  const queuesResult = checkQueues(provider.queues || []);

  // Calculate weighted overall percentage
  const overallPercentage = Math.round(
    (profilePictureResult.percentage * DEFAULT_COMPLETION_WEIGHTS.profilePicture +
     providerInfoResult.percentage * DEFAULT_COMPLETION_WEIGHTS.providerInfo +
     providingPlacesResult.percentage * DEFAULT_COMPLETION_WEIGHTS.providingPlaces +
     servicesResult.percentage * DEFAULT_COMPLETION_WEIGHTS.services +
     queuesResult.percentage * DEFAULT_COMPLETION_WEIGHTS.queues) / 100
  );

  // Determine completion status
  const overallCompleted = overallPercentage >= COMPLETION_THRESHOLDS.SETUP_COMPLETE;
  const shouldMarkAsComplete = overallPercentage === COMPLETION_THRESHOLDS.PERFECT_SETUP && !provider.isSetupComplete;

  const breakdown: CompletionBreakdown = {
    profilePicture: profilePictureResult,
    providerInfo: providerInfoResult,
    providingPlaces: providingPlacesResult,
    services: servicesResult,
    queues: queuesResult
  };

  return {
    overallPercentage,
    overallCompleted,
    breakdown,
    nextSteps: generateNextSteps(breakdown),
    criticalMissing: generateCriticalMissing(breakdown),
    shouldMarkAsComplete
  };
}

/**
 * Check profile picture completion (10% weight)
 */
function checkProfilePicture(provider: any): SectionCompletion {
  const hasLogo = !!(provider.logoId || provider.logo);
  
  return {
    completed: hasLogo,
    percentage: hasLogo ? 100 : 0,
    details: hasLogo ? 'Logo uploaded' : 'No logo uploaded'
  };
}

/**
 * Check provider information completion (30% weight)
 */
function checkProviderInfo(provider: any): ProviderInfoCompletion {
  const requiredFields = {
    title: !!(provider.title && provider.title.trim()),
    phone: !!(provider.phone && provider.phone.trim()),
    presentation: !!(provider.presentation && provider.presentation.trim()),
    category: !!(provider.providerCategoryId || provider.category)
  };

  const completedFields = Object.values(requiredFields).filter(Boolean).length;
  const totalFields = Object.keys(requiredFields).length;
  const percentage = Math.round((completedFields / totalFields) * 100);
  const completed = percentage === 100;

  const missingFields = Object.entries(requiredFields)
    .filter(([_, isComplete]) => !isComplete)
    .map(([field, _]) => field);

  let details: string;
  if (completed) {
    details = 'All provider information completed';
  } else if (missingFields.length === totalFields) {
    details = 'No provider information provided';
  } else {
    details = `Missing: ${missingFields.join(', ')}`;
  }

  return {
    completed,
    percentage,
    details,
    requiredFields
  };
}

/**
 * Check providing places completion (25% weight)
 */
function checkProvidingPlaces(places: any[]): LocationCompletion {
  const totalPlaces = places.length;
  
  if (totalPlaces === 0) {
    return {
      completed: false,
      percentage: 0,
      details: 'No locations added',
      count: 0,
      validPlaces: 0
    };
  }

  const validPlaces = places.filter(place => 
    place.name && place.name.trim() &&
    place.address && place.address.trim() &&
    place.city && place.city.trim()
  ).length;

  const percentage = Math.max(20, Math.round((validPlaces / totalPlaces) * 100));
  const completed = validPlaces > 0;

  let details: string;
  if (validPlaces === totalPlaces) {
    details = `All ${totalPlaces} location(s) have complete information`;
  } else if (validPlaces === 0) {
    details = `${totalPlaces} location(s) added but missing required information`;
  } else {
    details = `${validPlaces} of ${totalPlaces} location(s) have complete information`;
  }

  return {
    completed,
    percentage,
    details,
    count: totalPlaces,
    validPlaces
  };
}

/**
 * Check services completion (20% weight)
 */
function checkServices(services: any[]): ServiceCompletion {
  const count = services.length;
  const completed = count > 0;
  const percentage = completed ? 100 : 0;

  let details: string;
  if (count === 0) {
    details = 'No services configured';
  } else if (count === 1) {
    details = '1 service configured';
  } else {
    details = `${count} services configured`;
  }

  return {
    completed,
    percentage,
    details,
    count
  };
}

/**
 * Check queues completion (15% weight)
 */
function checkQueues(queues: any[]): QueueCompletion {
  const totalQueues = queues.length;
  const activeQueues = queues.filter(queue => queue.isActive).length;
  const completed = activeQueues > 0;
  const percentage = completed ? 100 : 0;

  let details: string;
  if (totalQueues === 0) {
    details = 'No queues configured';
  } else if (activeQueues === 0) {
    details = `${totalQueues} queue(s) configured but none are active`;
  } else if (activeQueues === 1) {
    details = '1 active queue configured';
  } else {
    details = `${activeQueues} active queue(s) configured`;
  }

  return {
    completed,
    percentage,
    details,
    count: totalQueues,
    activeQueues
  };
}

/**
 * Generate next steps based on completion breakdown
 */
function generateNextSteps(breakdown: CompletionBreakdown): string[] {
  const steps: string[] = [];

  if (!breakdown.providerInfo.completed) {
    const missing = Object.entries(breakdown.providerInfo.requiredFields)
      .filter(([_, isComplete]) => !isComplete)
      .map(([field, _]) => field);
    
    if (missing.length > 0) {
      steps.push(`Complete provider information: ${missing.join(', ')}`);
    }
  }

  if (!breakdown.providingPlaces.completed) {
    if (breakdown.providingPlaces.count === 0) {
      steps.push('Add at least one business location');
    } else {
      steps.push('Complete location information (name, address, city)');
    }
  }

  if (!breakdown.services.completed) {
    steps.push('Create at least one service offering');
  }

  if (!breakdown.profilePicture.completed) {
    steps.push('Upload a business logo');
  }

  if (!breakdown.queues.completed) {
    if (breakdown.queues.count === 0) {
      steps.push('Create at least one queue for appointment scheduling');
    } else {
      steps.push('Activate at least one queue for appointment scheduling');
    }
  }

  return steps;
}

/**
 * Generate critical missing items
 */
function generateCriticalMissing(breakdown: CompletionBreakdown): string[] {
  const critical: string[] = [];

  if (!breakdown.providerInfo.completed) {
    critical.push('Business information');
  }

  if (!breakdown.providingPlaces.completed) {
    critical.push('Business locations');
  }

  if (!breakdown.services.completed) {
    critical.push('Service offerings');
  }

  return critical;
}

/**
 * Create empty completion result for error cases
 */
function createEmptyCompletionResult(): ProfileCompletionResult {
  const emptySection: SectionCompletion = {
    completed: false,
    percentage: 0,
    details: 'No data available'
  };

  return {
    overallPercentage: 0,
    overallCompleted: false,
    breakdown: {
      profilePicture: emptySection,
      providerInfo: {
        ...emptySection,
        requiredFields: {
          title: false,
          phone: false,
          presentation: false,
          category: false
        }
      },
      providingPlaces: {
        ...emptySection,
        count: 0,
        validPlaces: 0
      },
      services: {
        ...emptySection,
        count: 0
      },
      queues: {
        ...emptySection,
        count: 0,
        activeQueues: 0
      }
    },
    nextSteps: ['Complete your provider profile setup'],
    criticalMissing: ['Unable to calculate completion status'],
    shouldMarkAsComplete: false
  };
}

/**
 * Safe wrapper for profile completion calculation
 */
export function calculateProfileCompletionSafe(
  data: Partial<ProfileCompletionData>
): ProfileCompletionResult {
  try {
    return calculateProfileCompletion(data as ProfileCompletionData);
  } catch (error) {
    console.error('Profile completion calculation failed:', error);
    return createEmptyCompletionResult();
  }
}
