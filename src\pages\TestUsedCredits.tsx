import React from 'react';
import PageMeta from '../components/common/PageMeta';
import { UsedCreditsCard } from '../components/subscription';
import { useUsedCredits } from '../hooks/useUsedCredits';
import Button from '../components/ui/button/Button';

export default function TestUsedCredits() {
  const { 
    fetchData, 
    refreshData, 
    isLoading, 
    error, 
    usedCredits,
    used,
    remaining,
    allocated,
    completed,
    usagePercentage,
    isNearLimit,
    isAtLimit,
    currentPeriod
  } = useUsedCredits();

  return (
    <>
      <PageMeta
        title="Test Used Credits | Provider Dashboard"
        description="Test page for used credits functionality"
      />

      <div className="space-y-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Used Credits Test Page
          </h1>
          
          <div className="flex gap-4 mb-6">
            <Button 
              onClick={fetchData}
              disabled={isLoading}
              size="sm"
            >
              {isLoading ? 'Loading...' : 'Fetch Credits Data'}
            </Button>
            
            <Button 
              onClick={refreshData}
              disabled={isLoading}
              variant="outline"
              size="sm"
            >
              Refresh Data
            </Button>
          </div>

          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4 mb-6">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">
                Error Loading Credits Data
              </h3>
              <p className="text-sm text-red-700 dark:text-red-300">
                {typeof error === 'string' ? error : 'An error occurred'}
              </p>
            </div>
          )}

          {usedCredits && (
            <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                Raw Data
              </h3>
              <pre className="text-sm text-gray-700 dark:text-gray-300 overflow-auto">
                {JSON.stringify(usedCredits, null, 2)}
              </pre>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                Used Credits
              </h4>
              <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                {used}
              </p>
            </div>
            
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-green-800 dark:text-green-200">
                Remaining Credits
              </h4>
              <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                {remaining}
              </p>
            </div>
            
            <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-purple-800 dark:text-purple-200">
                Monthly Allocated
              </h4>
              <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                {allocated}
              </p>
            </div>
            
            <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-orange-800 dark:text-orange-200">
                Completed Appointments
              </h4>
              <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                {completed}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Usage Percentage
              </h4>
              <p className="text-xl font-bold text-gray-900 dark:text-gray-100">
                {usagePercentage}%
              </p>
            </div>
            
            <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Near Limit
              </h4>
              <p className="text-xl font-bold text-gray-900 dark:text-gray-100">
                {isNearLimit ? 'Yes' : 'No'}
              </p>
            </div>
            
            <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                At Limit
              </h4>
              <p className="text-xl font-bold text-gray-900 dark:text-gray-100">
                {isAtLimit ? 'Yes' : 'No'}
              </p>
            </div>
          </div>

          {currentPeriod && (
            <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg mb-6">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Current Period
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Month: {currentPeriod.month}, Year: {currentPeriod.year}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Start: {currentPeriod.startDate.toLocaleDateString()}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                End: {currentPeriod.endDate.toLocaleDateString()}
              </p>
            </div>
          )}
        </div>

        {/* Used Credits Card Component */}
        <UsedCreditsCard />
      </div>
    </>
  );
}
