import React from 'react';
import Button from '../ui/button/Button';
import { useCustomerPortal, useSubscriptionStatus } from '../../hooks/useSubscription';
import toast from 'react-hot-toast';

interface CustomerPortalButtonProps {
  variant?: 'primary' | 'outline';
  size?: 'sm' | 'md';
  className?: string;
  children?: React.ReactNode;
  showIcon?: boolean;
  fullWidth?: boolean;
  disabled?: boolean;
}

export default function CustomerPortalButton({
  variant = 'outline',
  size = 'md',
  className = '',
  children,
  showIcon = true,
  fullWidth = false,
  disabled = false,
}: CustomerPortalButtonProps) {
  const { data: statusData, isLoading: statusLoading } = useSubscriptionStatus();
  const customerPortalMutation = useCustomerPortal();

  const handlePortalAccess = async () => {
    try {
      // Check if customer portal is available
      if (!statusData?.data?.hasCustomerPortal) {
        toast.error('Customer portal is not available. You need an active subscription first.');
        return;
      }

      // Trigger the portal access
      await customerPortalMutation.mutateAsync();
    } catch (error: any) {
      // Error is already handled by the mutation, but we can add additional handling here
      console.error('Portal access error:', error);
    }
  };

  const getButtonText = () => {
    if (children) return children;
    
    if (customerPortalMutation.isPending) {
      return 'Opening Portal...';
    }
    
    if (statusLoading) {
      return 'Loading...';
    }
    
    if (!statusData?.data?.hasCustomerPortal) {
      return 'Portal Unavailable';
    }
    
    return 'Manage Subscription';
  };

  const isButtonDisabled = () => {
    return (
      disabled ||
      statusLoading ||
      customerPortalMutation.isPending ||
      !statusData?.data?.hasCustomerPortal
    );
  };

  const getIcon = () => {
    if (!showIcon) return null;

    if (customerPortalMutation.isPending) {
      return (
        <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      );
    }

    if (!statusData?.data?.hasCustomerPortal) {
      return (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
        </svg>
      );
    }

    return (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
      </svg>
    );
  };

  return (
    <Button
      onClick={handlePortalAccess}
      variant={variant}
      size={size}
      disabled={isButtonDisabled()}
      className={`${fullWidth ? 'w-full' : ''} ${className}`}
      startIcon={getIcon()}
    >
      {getButtonText()}
    </Button>
  );
}

// Additional component for inline portal access with more context
interface CustomerPortalCardProps {
  className?: string;
}

export function CustomerPortalCard({ className = '' }: CustomerPortalCardProps) {
  const { data: statusData, isLoading } = useSubscriptionStatus();

  if (isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 animate-pulse ${className}`}>
        <div className="space-y-3">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
        </div>
      </div>
    );
  }

  const hasPortal = statusData?.data?.hasCustomerPortal;
  const subscription = statusData?.data?.subscription;

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 ${className}`}>
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          <div className={`p-2 rounded-lg ${hasPortal ? 'bg-blue-100 dark:bg-blue-900/20' : 'bg-gray-100 dark:bg-gray-700'}`}>
            <svg className={`w-5 h-5 ${hasPortal ? 'text-blue-600 dark:text-blue-400' : 'text-gray-400'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
        </div>
        
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-1">
            Subscription Management
          </h4>
          
          {hasPortal ? (
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Access your customer portal to manage your {subscription?.planName || 'subscription'}, 
              update payment methods, and view billing history.
            </p>
          ) : (
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Customer portal access requires an active subscription. 
              Upgrade your plan to access subscription management features.
            </p>
          )}
          
          <CustomerPortalButton
            variant={hasPortal ? 'primary' : 'outline'}
            size="sm"
          />
        </div>
      </div>
    </div>
  );
}

// Quick action component for dashboard widgets
interface QuickPortalAccessProps {
  className?: string;
}

export function QuickPortalAccess({ className = '' }: QuickPortalAccessProps) {
  const { data: statusData } = useSubscriptionStatus();
  
  if (!statusData?.data?.hasCustomerPortal) {
    return null;
  }

  return (
    <div className={`${className}`}>
      <CustomerPortalButton
        variant="outline"
        size="sm"
        showIcon={true}
        fullWidth={true}
      >
        Manage Billing
      </CustomerPortalButton>
    </div>
  );
}
