import { PaymentMethod, PaymentMethodsResponse } from '../types';

/**
 * Hardcoded payment methods configuration
 * Since the backend doesn't provide a methods endpoint, we define them here
 */
const HARDCODED_PAYMENT_METHODS: PaymentMethod[] = [
  {
    id: 'chargily',
    name: 'chargily',
    displayName: 'Chargily Pay',
    description: 'Local Algerian payment gateway supporting EDAHABIA and CIB',
    supportedMethods: ['edahabia', 'cib'],
    currency: 'DZD',
    isRecommended: true,
    isAvailable: true,
    region: ['DZ', 'Algeria'],
    features: [
      'Local Algerian support',
      'EDAHABIA & CIB cards',
      'DZD currency',
      'No international fees',
      'Instant processing',
      'Arabic/French support'
    ],
  },
  {
    id: 'lemonsqueezy',
    name: 'lemonsqueezy',
    displayName: 'LemonSqueezy',
    description: 'International payment processor supporting cards and PayPal',
    supportedMethods: ['card', 'paypal'],
    currency: 'USD',
    isRecommended: true,
    isAvailable: true,
    region: [], // Available globally
    features: [
      'International support',
      'Credit/Debit cards',
      'PayPal integration',
      'Multiple currencies',
      'Global coverage',
      'Instant processing'
    ],
  },
];

/**
 * Service for hardcoded payment methods
 */
export class HardcodedPaymentMethodsService {
  /**
   * Get available payment methods based on user location
   */
  static async getAvailablePaymentMethods(userCountryCode?: string): Promise<PaymentMethodsResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    const isAlgerian = userCountryCode === 'DZ' || userCountryCode?.toLowerCase().includes('algeria');
    
    // Filter methods based on location
    const availableMethods = HARDCODED_PAYMENT_METHODS.filter(method => {
      // Chargily only for Algerian users
      if (method.id === 'chargily') {
        return isAlgerian;
      }
      
      // LemonSqueezy for all users
      if (method.id === 'lemonsqueezy') {
        return true;
      }
      
      return method.isAvailable;
    });

    // Determine recommended method
    const recommendedMethod = isAlgerian 
      ? availableMethods.find(m => m.id === 'chargily')?.id || 'lemonsqueezy'
      : 'lemonsqueezy';

    return {
      success: true,
      message: 'Payment methods retrieved successfully',
      data: {
        methods: availableMethods,
        recommendedMethod,
        providerCountry: isAlgerian ? 'Algeria' : 'International',
      },
    };
  }

  /**
   * Get all payment methods (for admin/testing)
   */
  static async getAllPaymentMethods(): Promise<PaymentMethodsResponse> {
    await new Promise(resolve => setTimeout(resolve, 200));

    return {
      success: true,
      message: 'All payment methods retrieved successfully',
      data: {
        methods: HARDCODED_PAYMENT_METHODS,
        recommendedMethod: 'lemonsqueezy',
        providerCountry: 'Unknown',
      },
    };
  }

  /**
   * Get payment method by ID
   */
  static getPaymentMethodById(methodId: string): PaymentMethod | null {
    return HARDCODED_PAYMENT_METHODS.find(method => method.id === methodId) || null;
  }

  /**
   * Check if payment method is available for location
   */
  static isPaymentMethodAvailable(methodId: string, userCountryCode?: string): boolean {
    const method = this.getPaymentMethodById(methodId);
    if (!method) return false;

    const isAlgerian = userCountryCode === 'DZ' || userCountryCode?.toLowerCase().includes('algeria');

    // Chargily only for Algerian users
    if (method.id === 'chargily') {
      return isAlgerian;
    }

    // LemonSqueezy for all users
    if (method.id === 'lemonsqueezy') {
      return true;
    }

    return method.isAvailable;
  }

  /**
   * Get recommended payment method for location
   */
  static getRecommendedPaymentMethod(userCountryCode?: string): PaymentMethod | null {
    const isAlgerian = userCountryCode === 'DZ' || userCountryCode?.toLowerCase().includes('algeria');
    
    if (isAlgerian) {
      const chargilyMethod = this.getPaymentMethodById('chargily');
      if (chargilyMethod && this.isPaymentMethodAvailable('chargily', userCountryCode)) {
        return chargilyMethod;
      }
    }

    // Fallback to LemonSqueezy
    return this.getPaymentMethodById('lemonsqueezy');
  }

  /**
   * Get payment methods for specific region
   */
  static getPaymentMethodsForRegion(region: string): PaymentMethod[] {
    const isAlgerian = region === 'DZ' || region.toLowerCase().includes('algeria');
    
    return HARDCODED_PAYMENT_METHODS.filter(method => {
      if (method.id === 'chargily') {
        return isAlgerian;
      }
      
      if (method.id === 'lemonsqueezy') {
        return true;
      }
      
      return method.region?.includes(region) || method.region?.length === 0;
    });
  }

  /**
   * Get currency for payment method
   */
  static getCurrencyForPaymentMethod(methodId: string): string {
    const method = this.getPaymentMethodById(methodId);
    return method?.currency || 'USD';
  }

  /**
   * Get supported payment methods for processor
   */
  static getSupportedMethodsForProcessor(processorId: string): string[] {
    const method = this.getPaymentMethodById(processorId);
    return method?.supportedMethods || [];
  }

  /**
   * Validate payment method selection
   */
  static validatePaymentMethodSelection(
    processorId: string,
    paymentMethod: string,
    userCountryCode?: string
  ): { isValid: boolean; error?: string } {
    const processor = this.getPaymentMethodById(processorId);
    
    if (!processor) {
      return {
        isValid: false,
        error: `Payment processor "${processorId}" is not available`,
      };
    }

    if (!this.isPaymentMethodAvailable(processorId, userCountryCode)) {
      return {
        isValid: false,
        error: `${processor.displayName} is not available in your region`,
      };
    }

    if (!processor.supportedMethods.includes(paymentMethod)) {
      return {
        isValid: false,
        error: `Payment method "${paymentMethod}" is not supported by ${processor.displayName}`,
      };
    }

    return { isValid: true };
  }

  /**
   * Get payment method features for display
   */
  static getPaymentMethodFeatures(methodId: string): {
    supportedMethods: string[];
    currency: string;
    features: string[];
    benefits: string[];
  } {
    const method = this.getPaymentMethodById(methodId);
    
    if (!method) {
      return {
        supportedMethods: [],
        currency: 'USD',
        features: [],
        benefits: [],
      };
    }

    const benefits: string[] = [];
    
    if (method.id === 'chargily') {
      benefits.push('Local Algerian support');
      benefits.push('DZD currency');
      benefits.push('No international fees');
    }
    
    if (method.id === 'lemonsqueezy') {
      benefits.push('International support');
      benefits.push('Multiple currencies');
      benefits.push('Instant processing');
    }

    return {
      supportedMethods: method.supportedMethods,
      currency: method.currency,
      features: method.features,
      benefits,
    };
  }
}
