/**
 * Profile completion types and interfaces
 * Based on the profile-completion-docs.md specification
 */

import { User, ProviderCategory, File } from './auth';
import { Provider, Location, Service, Queue } from './provider';

// Main completion data interface
export interface ProfileCompletionData {
  user: User;
  provider: Provider & {
    category?: ProviderCategory | null;
    logo?: File | null;
    providingPlaces?: (Location & {
      detailedAddress?: Address;
      queues?: Queue[];
    })[];
    services?: Service[];
    queues?: Queue[];
  };
}

// Section completion base interface
export interface SectionCompletion {
  completed: boolean;
  percentage: number;
  details: string;
}

// Provider info specific completion
export interface ProviderInfoCompletion extends SectionCompletion {
  requiredFields: {
    title: boolean;
    phone: boolean;
    presentation: boolean;
    category: boolean;
  };
}

// Location completion interface
export interface LocationCompletion extends SectionCompletion {
  count: number;
  validPlaces: number;
}

// Service completion interface
export interface ServiceCompletion extends SectionCompletion {
  count: number;
}

// Queue completion interface
export interface QueueCompletion extends SectionCompletion {
  count: number;
  activeQueues: number;
}

// Detailed breakdown for each section
export interface CompletionBreakdown {
  profilePicture: SectionCompletion;
  providerInfo: ProviderInfoCompletion;
  providingPlaces: LocationCompletion;
  services: ServiceCompletion;
  queues: QueueCompletion;
}

// Final result interface
export interface ProfileCompletionResult {
  overallPercentage: number;
  overallCompleted: boolean;
  breakdown: CompletionBreakdown;
  nextSteps: string[];
  criticalMissing: string[];
  shouldMarkAsComplete: boolean;
}

// API Response interface
export interface ProfileCompletionResponse {
  success: boolean;
  data: ProfileCompletionResult;
  message: string;
}

// Section weights for calculation
export interface CompletionWeights {
  profilePicture: number;
  providerInfo: number;
  providingPlaces: number;
  services: number;
  queues: number;
}

// Default weights as per documentation
export const DEFAULT_COMPLETION_WEIGHTS: CompletionWeights = {
  profilePicture: 10,    // Logo upload
  providerInfo: 30,      // Business information
  providingPlaces: 25,   // Locations
  services: 20,          // Service offerings
  queues: 15            // Scheduling queues
};

// Completion thresholds
export const COMPLETION_THRESHOLDS = {
  SETUP_COMPLETE: 80,    // Considered functional
  PERFECT_SETUP: 100,    // All sections complete
} as const;

// Address interface (referenced in Location)
export interface Address {
  id: number;
  street?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  latitude?: number;
  longitude?: number;
}

// Complete setup request interface for mobile apps
export interface CompleteSetupRequest {
  providerInfo: {
    title: string;
    phone: string;
    presentation: string;
    providerCategoryId: number;
  };
  locations: Array<{
    name: string;
    address: string;
    city: string;
    shortName?: string;
    mobile?: string;
    timezone?: string;
    parking?: boolean;
    elevator?: boolean;
    handicapAccess?: boolean;
  }>;
  services: Array<{
    title: string;
    duration: number;
    price: number;
    pointsRequirements?: number;
    deliveryType: 'at_location' | 'at_customer' | 'both';
    description?: string;
    color: string;
    isPublic?: boolean;
    acceptOnline?: boolean;
    acceptNew?: boolean;
    notificationOn?: boolean;
  }>;
  queues: Array<{
    title: string;
    isActive: boolean;
    sProvidingPlaceId: number;
  }>;
  logo?: File;
}

// Complete setup response interface
export interface CompleteSetupResponse {
  success: boolean;
  data: {
    provider: Provider;
    completion: ProfileCompletionResult;
    summary: {
      totalSteps: number;
      completedSteps: number;
      createdLocations: number;
      createdServices: number;
      createdQueues: number;
      logoUploaded: boolean;
    };
  };
  message: string;
}

// Error response interface
export interface ProfileCompletionError {
  success: false;
  message: string;
  error?: string; // Detailed error information (development only)
}

// Union type for all possible responses
export type ProfileCompletionApiResponse = ProfileCompletionResponse | ProfileCompletionError;

// Hook return type
export interface UseProfileCompletionReturn {
  completion: ProfileCompletionResult | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

// Component props interfaces
export interface ProfileCompletionCardProps {
  data: ProfileCompletionData;
  showDetails?: boolean;
  onActionClick?: (section: string, action: string) => void;
  onDismiss?: () => void;
  className?: string;
}

export interface ProfileCompletionExampleProps {
  userId: string;
  showDetails?: boolean;
  className?: string;
  onDismiss?: () => void;
}

export interface ProfileCompletionManagerProps {
  children?: React.ReactNode;
  resetOnMount?: boolean;
}

// Navigation action types
export type CompletionSection = 'profilePicture' | 'providerInfo' | 'providingPlaces' | 'services' | 'queues';
export type CompletionAction = 'complete' | 'edit' | 'add' | 'upload';

// Local storage keys
export const PROFILE_COMPLETION_STORAGE_KEYS = {
  DISMISSED_CARD: 'profile-completion-dismissed',
  LAST_COMPLETION_CHECK: 'profile-completion-last-check',
  COMPLETION_CACHE: 'profile-completion-cache',
} as const;

// Cache configuration
export interface CompletionCacheConfig {
  ttl: number; // Time to live in milliseconds
  maxAge: number; // Maximum age before forced refresh
}

export const DEFAULT_CACHE_CONFIG: CompletionCacheConfig = {
  ttl: 5 * 60 * 1000, // 5 minutes
  maxAge: 15 * 60 * 1000, // 15 minutes
};
