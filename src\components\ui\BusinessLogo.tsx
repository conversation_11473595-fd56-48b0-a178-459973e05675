import React from 'react';
import { useHasProviderLogo } from '../../hooks/useProvider';
import { BoxIcon } from '../../icons';

export interface BusinessLogoProps {
  size?: 'small' | 'medium' | 'large' | 'xlarge';
  className?: string;
  fallbackSrc?: string;
  alt?: string;
  shape?: 'square' | 'rounded';
}

const sizeClasses = {
  small: 'w-8 h-8',
  medium: 'w-11 h-11', 
  large: 'w-16 h-16',
  xlarge: 'w-24 h-24',
};

const iconSizeClasses = {
  small: 'w-5 h-5',
  medium: 'w-8 h-8',
  large: 'w-10 h-10', 
  xlarge: 'w-16 h-16',
};

const shapeClasses = {
  square: 'rounded-lg',
  rounded: 'rounded-full',
};

export default function BusinessLogo({
  size = 'medium',
  className = '',
  fallbackSrc,
  alt = 'Business Logo',
  shape = 'square',
}: BusinessLogoProps) {
  const { hasLogo, logoUrl, isLoading } = useHasProviderLogo();

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const img = e.currentTarget;
    const icon = img.nextElementSibling as HTMLElement;
    
    // Hide the failed image and show the icon
    img.style.display = 'none';
    if (icon) {
      icon.classList.remove('hidden');
    }
  };

  const displayImageUrl = logoUrl || fallbackSrc;

  return (
    <div className={`relative overflow-hidden bg-gray-100 dark:bg-gray-800 flex items-center justify-center ${sizeClasses[size]} ${shapeClasses[shape]} ${className}`}>
      {/* Loading state */}
      {isLoading && (
        <div className={`animate-pulse bg-gray-200 dark:bg-gray-700 w-full h-full ${shapeClasses[shape]}`} />
      )}
      
      {/* Business logo or fallback image */}
      {!isLoading && displayImageUrl && (
        <img
          src={displayImageUrl}
          alt={alt}
          className="w-full h-full object-cover"
          onError={handleImageError}
        />
      )}
      
      {/* Fallback icon */}
      <BoxIcon 
        className={`text-gray-400 ${iconSizeClasses[size]} ${
          !isLoading && displayImageUrl ? 'hidden' : ''
        }`}
      />
    </div>
  );
}
