import React from 'react';
import { Navigate, useLocation } from 'react-router';
import { useAuth } from '../../context/AuthContext';
import { ErrorDisplay } from '../error';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireSetupComplete?: boolean;
  fallbackPath?: string;
}

/**
 * Protected route component that requires authentication
 */
export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireSetupComplete = false,
  fallbackPath = '/signin',
}) => {
  const { isAuthenticated, isLoading, provider, error } = useAuth();
  const location = useLocation();

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-500 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  // Show error state if authentication failed
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
        <div className="max-w-md w-full">
          <ErrorDisplay
            error={error}
            title="Authentication Error"
            variant="card"
            showRetry
            onRetry={() => window.location.reload()}
          />
        </div>
      </div>
    );
  }

  // Redirect to signin if not authenticated
  if (!isAuthenticated) {
    if (import.meta.env.DEV) {
      console.log('🔒 ProtectedRoute: Redirecting to signin because not authenticated');
      console.trace('ProtectedRoute redirect stack trace');
    }
    return (
      <Navigate
        to={fallbackPath}
        state={{ from: location.pathname }}
        replace
      />
    );
  }

  // Check if setup completion is required
  if (requireSetupComplete && provider && !provider.isSetupComplete) {
    return (
      <Navigate
        to="/setup"
        state={{ from: location.pathname }}
        replace
      />
    );
  }

  return <>{children}</>;
};

/**
 * Public route component that redirects authenticated users
 */
interface PublicRouteProps {
  children: React.ReactNode;
  redirectPath?: string;
}

export const PublicRoute: React.FC<PublicRouteProps> = ({
  children,
  redirectPath = '/',
}) => {
  const { isAuthenticated, isLoading } = useAuth();

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-500 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  // Redirect to dashboard if already authenticated
  if (isAuthenticated) {
    if (import.meta.env.DEV) {
      console.log('🔓 PublicRoute: Redirecting to dashboard because already authenticated');
      console.trace('PublicRoute redirect stack trace');
    }
    return <Navigate to={redirectPath} replace />;
  }

  return <>{children}</>;
};

/**
 * Setup completion guard
 */
interface SetupGuardProps {
  children: React.ReactNode;
}

export const SetupGuard: React.FC<SetupGuardProps> = ({ children }) => {
  const { provider, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-500 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  // Redirect to setup if not completed
  if (provider && !provider.isSetupComplete) {
    return <Navigate to="/setup" replace />;
  }

  return <>{children}</>;
};

/**
 * Higher-order component for route protection
 */
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options: {
    requireSetupComplete?: boolean;
    fallbackPath?: string;
  } = {}
) {
  const WrappedComponent = (props: P) => (
    <ProtectedRoute
      requireSetupComplete={options.requireSetupComplete}
      fallbackPath={options.fallbackPath}
    >
      <Component {...props} />
    </ProtectedRoute>
  );

  WrappedComponent.displayName = `withAuth(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

/**
 * Higher-order component for public routes
 */
export function withPublicRoute<P extends object>(
  Component: React.ComponentType<P>,
  redirectPath?: string
) {
  const WrappedComponent = (props: P) => (
    <PublicRoute redirectPath={redirectPath}>
      <Component {...props} />
    </PublicRoute>
  );

  WrappedComponent.displayName = `withPublicRoute(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

export default ProtectedRoute;
