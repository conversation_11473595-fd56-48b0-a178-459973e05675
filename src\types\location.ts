/**
 * Location related types
 */

import { Provider } from './provider';

export interface Location {
  id: number;
  sProviderId: number;
  name: string;
  shortName?: string;
  address?: string;
  city?: string;
  mobile?: string;
  isMobileHidden: boolean;
  fax?: string;
  floor?: string;
  parking: boolean;
  elevator: boolean;
  handicapAccess: boolean;
  timezone?: string;
  detailedAddressId?: number;
  createdAt: string;
  updatedAt: string;

  // Flat address fields (as returned by API)
  country?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;

  // Opening hours (as returned by API)
  openingHours?: OpeningHoursDay[];

  // Relations
  provider?: Provider;
  appointments?: Appointment[];
  openings?: Opening[];
  queues?: Queue[];
  detailedAddress?: Address;
}

export interface Address {
  id: number;
  street?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  createdAt: string;
  updatedAt: string;
}

export interface LocationCreateRequest {
  name: string;
  shortName?: string;
  address?: string;
  city?: string;
  mobile: string; // Required as per API schema
  isMobileHidden?: boolean;
  fax?: string;
  floor?: string;
  parking?: boolean;
  elevator?: boolean;
  handicapAccess?: boolean;
  timezone?: string;
  // Flat address structure as per API
  country?: string;
  postalCode?: string;
  latitude: number;
  longitude: number;
  // Opening hours (optional)
  openingHours?: OpeningHoursDay[];
}

export interface AddressCreateRequest {
  street?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
}

export interface LocationUpdateRequest extends Partial<LocationCreateRequest> {
  id: number;
}

export interface OpeningHoursDay {
  dayOfWeek: string; // "Monday", "Tuesday", etc.
  isActive: boolean;
  hours: TimeSlot[]; // Changed from timeSlots to hours to match API
}

export interface TimeSlot {
  timeFrom: string; // "09:00"
  timeTo: string;   // "17:00"
}

export interface Opening {
  id: number;
  sProvidingPlaceId: number;
  dayOfWeek: string;
  timeFrom: string;
  timeTo: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  
  // Relations
  location?: Location;
}

export interface LocationFilters {
  city?: string;
  hasParking?: boolean;
  hasElevator?: boolean;
  hasHandicapAccess?: boolean;
  search?: string;
}

export interface LocationStats {
  totalLocations: number;
  activeLocations: number;
  citiesServed: string[];
  averageRating?: number;
  mostPopularLocation?: Location;
}

// Forward declarations
export interface Appointment {
  id: number;
  sProvidingPlaceId?: number;
  location?: Location;
  // Other appointment fields...
}

export interface Queue {
  id: number;
  sProvidingPlaceId: number;
  location?: Location;
  // Other queue fields...
}
