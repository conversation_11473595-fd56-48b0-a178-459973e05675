import React, { useState } from 'react';
import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import PageMeta from "../../components/common/PageMeta";
import Button from "../../components/ui/button/Button";
import { ErrorDisplay } from "../../components/error";
import { useCustomers, useCustomerStats } from "../../hooks/useCRM";
import CustomerCard from "../../components/crm/CustomerCard";
import CustomerForm from "../../components/crm/CustomerForm";
import CustomerDetails from "../../components/crm/CustomerDetails";
import CustomerStatsOverview from "../../components/crm/CustomerStatsOverview";
import CustomerSegments from "../../components/crm/CustomerSegments";
import { Customer, CustomerFilters } from "../../types/customer";

export default function CustomerManagement() {
  const [showCustomerForm, setShowCustomerForm] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [showSegments, setShowSegments] = useState(false);
  const [filters, setFilters] = useState<CustomerFilters>({});
  const [viewMode, setViewMode] = useState<'cards' | 'table'>('cards');

  const { data: customers, isLoading, error } = useCustomers(filters);
  const { data: customerStats } = useCustomerStats();

  const handleCreateCustomer = () => {
    setEditingCustomer(null);
    setShowCustomerForm(true);
  };

  const handleEditCustomer = (customer: Customer) => {
    setEditingCustomer(customer);
    setShowCustomerForm(true);
  };

  const handleViewCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setShowDetails(true);
  };

  const handleCloseForm = () => {
    setShowCustomerForm(false);
    setEditingCustomer(null);
  };

  const handleCloseDetails = () => {
    setShowDetails(false);
    setSelectedCustomer(null);
  };

  const handleCustomerSuccess = () => {
    handleCloseForm();
  };

  const handleFilterChange = (newFilters: Partial<CustomerFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <ErrorDisplay
          error={error}
          title="Failed to load customers"
          variant="card"
          showRetry
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <>
      <PageMeta
        title="Customer Management | Provider Dashboard"
        description="Manage customer relationships, communications, and insights"
      />
      <PageBreadcrumb pageTitle="Customer Management" />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Customer Relationship Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Manage customer relationships, track interactions, and gain insights
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={() => setShowSegments(true)}
              variant="outline"
              size="sm"
            >
              Customer Segments
            </Button>
            <Button
              onClick={handleCreateCustomer}
              size="sm"
            >
              Add New Customer
            </Button>
          </div>
        </div>

        {/* Customer Statistics Overview */}
        {customerStats && <CustomerStatsOverview stats={customerStats} />}

        {/* Filters and View Controls */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-4">
            <div className="flex items-center space-x-4">
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                Filters
              </h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setViewMode('cards')}
                  className={`p-2 rounded-lg transition-colors ${
                    viewMode === 'cards'
                      ? 'bg-brand-100 text-brand-600 dark:bg-brand-900/20 dark:text-brand-400'
                      : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
                  }`}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                  </svg>
                </button>
                <button
                  onClick={() => setViewMode('table')}
                  className={`p-2 rounded-lg transition-colors ${
                    viewMode === 'table'
                      ? 'bg-brand-100 text-brand-600 dark:bg-brand-900/20 dark:text-brand-400'
                      : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
                  }`}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Status
              </label>
              <select
                value={filters.isActive === undefined ? '' : filters.isActive ? 'active' : 'inactive'}
                onChange={(e) => {
                  const value = e.target.value;
                  handleFilterChange({ 
                    isActive: value === '' ? undefined : value === 'active'
                  });
                }}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="">All Customers</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Membership Level
              </label>
              <select
                value={filters.membershipLevel || ''}
                onChange={(e) => handleFilterChange({ 
                  membershipLevel: e.target.value as any || undefined 
                })}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="">All Levels</option>
                <option value="bronze">Bronze</option>
                <option value="silver">Silver</option>
                <option value="gold">Gold</option>
                <option value="platinum">Platinum</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Min Visits
              </label>
              <input
                type="number"
                min="0"
                value={filters.totalVisitsMin || ''}
                onChange={(e) => handleFilterChange({ 
                  totalVisitsMin: e.target.value ? Number(e.target.value) : undefined 
                })}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                placeholder="Min visits"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Min Spent ($)
              </label>
              <input
                type="number"
                min="0"
                value={filters.totalSpentMin || ''}
                onChange={(e) => handleFilterChange({ 
                  totalSpentMin: e.target.value ? Number(e.target.value) : undefined 
                })}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                placeholder="Min spent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Search
              </label>
              <input
                type="text"
                placeholder="Search customers..."
                value={filters.search || ''}
                onChange={(e) => handleFilterChange({ search: e.target.value || undefined })}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 placeholder:text-gray-400 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-500"
              />
            </div>
          </div>
        </div>

        {/* Customers List */}
        {customers && customers.length > 0 ? (
          <div className={viewMode === 'cards' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
            {customers.map((customer) => (
              <CustomerCard
                key={customer.id}
                customer={customer}
                viewMode={viewMode}
                onView={() => handleViewCustomer(customer)}
                onEdit={() => handleEditCustomer(customer)}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="mx-auto h-24 w-24 text-gray-400 mb-4">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No customers found
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              {Object.keys(filters).length > 0 
                ? "No customers match your current filters. Try adjusting your search criteria."
                : "Get started by adding your first customer to build relationships."
              }
            </p>
            {Object.keys(filters).length === 0 && (
              <Button onClick={handleCreateCustomer}>
                Add Your First Customer
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Customer Form Modal */}
      {showCustomerForm && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-screen items-start justify-center p-4 pt-16 pb-8">
            <div className="fixed inset-0 bg-black bg-opacity-25" onClick={handleCloseForm} />
            <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[85vh] overflow-y-auto my-8">
              <CustomerForm
                customer={editingCustomer}
                onClose={handleCloseForm}
                onSuccess={handleCustomerSuccess}
              />
            </div>
          </div>
        </div>
      )}

      {/* Customer Details Modal */}
      {showDetails && selectedCustomer && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-screen items-start justify-center p-4 pt-16 pb-8">
            <div className="fixed inset-0 bg-black bg-opacity-25" onClick={handleCloseDetails} />
            <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-6xl w-full max-h-[85vh] overflow-y-auto my-8">
              <CustomerDetails
                customer={selectedCustomer}
                onClose={handleCloseDetails}
                onEdit={() => {
                  setEditingCustomer(selectedCustomer);
                  setShowDetails(false);
                  setShowCustomerForm(true);
                }}
              />
            </div>
          </div>
        </div>
      )}

      {/* Customer Segments Modal */}
      {showSegments && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-screen items-start justify-center p-4 pt-16 pb-8">
            <div className="fixed inset-0 bg-black bg-opacity-25" onClick={() => setShowSegments(false)} />
            <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[85vh] overflow-y-auto my-8">
              <CustomerSegments onClose={() => setShowSegments(false)} />
            </div>
          </div>
        </div>
      )}
    </>
  );
}
