import React from 'react';
import { SubscriptionPlan } from '../../types';
import { useSubscriptionPlans, useSubscriptionStatus, useCreateCheckoutSession } from '../../hooks/useSubscription';
import SubscriptionPlanCard from './SubscriptionPlanCard';
import { ErrorDisplay } from '../error';

interface SubscriptionPlansGridProps {
  className?: string;
  showRecommended?: boolean;
  onPlanSelect?: (planId: string) => void;
}

export default function SubscriptionPlansGrid({
  className = '',
  showRecommended = true,
  onPlanSelect,
}: SubscriptionPlansGridProps) {
  const { data: plansData, isLoading: plansLoading, error: plansError } = useSubscriptionPlans();
  const { data: statusData, isLoading: statusLoading } = useSubscriptionStatus();
  const createCheckoutMutation = useCreateCheckoutSession();

  const handlePlanSelect = (planId: string) => {
    if (onPlanSelect) {
      onPlanSelect(planId);
      return;
    }

    // Default behavior: create checkout session
    createCheckoutMutation.mutate({ planId });
  };

  const getCurrentPlanId = () => {
    return statusData?.data?.subscription?.planId;
  };

  const getRecommendedPlanId = () => {
    if (!showRecommended || !plansData?.data?.plans) return null;
    
    // Recommend the "pro" plan as it's described as "Our most popular plan"
    const proPlan = plansData.data.plans.find(plan => plan.id === 'pro');
    return proPlan?.id || null;
  };

  const sortPlans = (plans: SubscriptionPlan[]) => {
    // Sort plans: Free first, then subscriptions by price, then one-time purchases
    return [...plans].sort((a, b) => {
      // Free plan always first
      if (a.id === 'free') return -1;
      if (b.id === 'free') return 1;
      
      // Subscriptions before one-time purchases
      if (a.isSubscription && !b.isSubscription) return -1;
      if (!a.isSubscription && b.isSubscription) return 1;
      
      // Within subscriptions, sort by credits amount
      if (a.isSubscription && b.isSubscription) {
        return a.effect.amount - b.effect.amount;
      }
      
      // Within one-time purchases, sort by credits amount
      return a.effect.amount - b.effect.amount;
    });
  };

  if (plansLoading || statusLoading) {
    return (
      <div className={`${className}`}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-gray-200 dark:bg-gray-700 rounded-2xl h-96"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (plansError) {
    return (
      <div className={`${className}`}>
        <ErrorDisplay 
          error={plansError} 
          title="Failed to load subscription plans"
          message="Please try refreshing the page or contact support if the problem persists."
        />
      </div>
    );
  }

  if (!plansData?.data?.plans || plansData.data.plans.length === 0) {
    return (
      <div className={`${className}`}>
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No subscription plans available
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            Please check back later or contact support.
          </p>
        </div>
      </div>
    );
  }

  const currentPlanId = getCurrentPlanId();
  const recommendedPlanId = getRecommendedPlanId();
  const sortedPlans = sortPlans(plansData.data.plans);

  return (
    <div className={`${className}`}>
      {/* Header */}
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Choose Your Plan
        </h2>
        <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          Select the perfect plan for your business needs. Upgrade or downgrade at any time.
        </p>
      </div>

      {/* Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {sortedPlans.map((plan) => (
          <SubscriptionPlanCard
            key={plan.id}
            plan={plan}
            isCurrentPlan={plan.id === currentPlanId}
            isRecommended={plan.id === recommendedPlanId}
            isLoading={createCheckoutMutation.isPending}
            onSubscribe={handlePlanSelect}
            onUpgrade={handlePlanSelect}
          />
        ))}
      </div>

      {/* Additional Info */}
      <div className="mt-8 text-center">
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 max-w-2xl mx-auto">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5 text-blue-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="text-left">
              <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
                Need help choosing?
              </h4>
              <p className="text-sm text-blue-700 dark:text-blue-200">
                All plans include our core features. You can upgrade or downgrade at any time, 
                and unused credits never expire.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Billing Info */}
      <div className="mt-6 text-center">
        <p className="text-xs text-gray-500 dark:text-gray-400">
          Secure billing powered by LemonSqueezy. Cancel anytime.
        </p>
      </div>
    </div>
  );
}
