import React from 'react';
import Button from '../ui/button/Button';
import { ProviderCustomer } from '../../types/provider-customer';
import { UserCircleIcon, PhoneIcon, EnvelopeIcon, IdentificationIcon, CalendarIcon } from '../../icons';

interface ProviderCustomerCardProps {
  customer: ProviderCustomer;
  viewMode?: 'cards' | 'table';
  onView: () => void;
  onEdit: () => void;
  onDelete?: () => void;
  isDeleting?: boolean;
}

export default function ProviderCustomerCard({ 
  customer, 
  viewMode = 'cards', 
  onView, 
  onEdit, 
  onDelete,
  isDeleting = false 
}: ProviderCustomerCardProps) {
  
  const getInitials = () => {
    const firstInitial = customer.firstName?.charAt(0)?.toUpperCase() || '';
    const lastInitial = customer.lastName?.charAt(0)?.toUpperCase() || '';
    return `${firstInitial}${lastInitial}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = () => {
    return customer.isActive !== false 
      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
  };

  const getStatusText = () => {
    return customer.isActive !== false ? 'Active' : 'Inactive';
  };

  if (viewMode === 'table') {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 hover:shadow-md transition-shadow cursor-pointer"
           onClick={onView}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* Avatar */}
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
              {getInitials()}
            </div>
            
            {/* Basic Info */}
            <div>
              <h3 className="font-medium text-gray-900 dark:text-white">
                {customer.firstName} {customer.lastName}
              </h3>
              <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                <PhoneIcon className="w-3 h-3" />
                <span>{customer.mobileNumber}</span>
                {customer.email && (
                  <>
                    <span>•</span>
                    <EnvelopeIcon className="w-3 h-3" />
                    <span>{customer.email}</span>
                  </>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Appointment Count */}
            <div className="text-center">
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                {customer.appointmentCount}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Appointments
              </div>
            </div>
            
            {/* Status */}
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor()}`}>
              {getStatusText()}
            </span>
            
            {/* Actions */}
            <div className="flex items-center space-x-2">
              <Button
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit();
                }}
                variant="outline"
                size="sm"
                className="text-xs"
              >
                Edit
              </Button>
              {onDelete && (
                <Button
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete();
                  }}
                  variant="outline"
                  size="sm"
                  className="text-xs text-red-600 hover:text-red-700 hover:border-red-300"
                  disabled={isDeleting}
                >
                  {isDeleting ? 'Removing...' : 'Remove'}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow cursor-pointer"
         onClick={onView}>
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium">
            {getInitials()}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {customer.firstName} {customer.lastName}
            </h3>
            <div className="flex items-center space-x-1 text-sm text-gray-500 dark:text-gray-400">
              <PhoneIcon className="w-3 h-3" />
              <span>{customer.mobileNumber}</span>
            </div>
          </div>
        </div>
        
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor()}`}>
          {getStatusText()}
        </span>
      </div>

      {/* Contact Information */}
      <div className="space-y-2 mb-4">
        {customer.email && (
          <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
            <EnvelopeIcon className="w-4 h-4 text-gray-400" />
            <span>{customer.email}</span>
          </div>
        )}
        
        {customer.nationalId && (
          <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
            <IdentificationIcon className="w-4 h-4 text-gray-400" />
            <span>{customer.nationalId}</span>
          </div>
        )}
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            {customer.appointmentCount}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Appointments
          </div>
        </div>
        
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            <CalendarIcon className="w-5 h-5 mx-auto" />
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Customer Since
          </div>
        </div>
      </div>

      {/* Notes Preview */}
      {customer.notes && (
        <div className="mb-4">
          <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
            <span className="font-medium">Notes:</span> {customer.notes}
          </p>
        </div>
      )}

      {/* Footer */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="text-sm text-gray-500 dark:text-gray-400">
          Added: {formatDate(customer.createdAt)}
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            onClick={(e) => {
              e.stopPropagation();
              onEdit();
            }}
            variant="outline"
            size="sm"
            className="text-xs"
          >
            Edit
          </Button>
          {onDelete && (
            <Button
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
              variant="outline"
              size="sm"
              className="text-xs text-red-600 hover:text-red-700 hover:border-red-300"
              disabled={isDeleting}
            >
              {isDeleting ? 'Removing...' : 'Remove'}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
