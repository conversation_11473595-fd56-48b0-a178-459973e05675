import { useAuth } from "../context/AuthContext";

export default function SidebarWidget() {
  const { provider } = useAuth();

  return (
    <div
      className={`
        mx-auto mb-10 w-full max-w-60 rounded-2xl bg-gray-50 px-4 py-5 text-center dark:bg-white/[0.03]`}
    >
      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg mx-auto mb-3">
        {provider?.title ? provider.title.charAt(0).toUpperCase() : 'P'}
      </div>
      <h3 className="mb-2 font-semibold text-gray-900 dark:text-white">
        {provider?.title || 'Your Business'}
      </h3>
      <p className="mb-4 text-gray-500 text-theme-sm dark:text-gray-400">
        Manage your appointments, services, and grow your business with our provider dashboard.
      </p>
      <button className="flex items-center justify-center p-3 font-medium text-white rounded-lg bg-brand-500 text-theme-sm hover:bg-brand-600 w-full">
        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        Quick Setup
      </button>
    </div>
  );
}
