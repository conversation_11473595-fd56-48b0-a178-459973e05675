/**
 * Timezone utility functions for handling local time display and UTC API submission
 */

/**
 * Convert a local datetime string to UTC ISO string for API submission
 * @param localDateTimeString - Local datetime string from datetime-local input (e.g., "2024-01-15T14:30")
 * @returns UTC ISO string (e.g., "2024-01-15T19:30:00.000Z")
 */
export function localDateTimeToUTC(localDateTimeString: string): string {
  if (!localDateTimeString) return '';
  
  // Create a Date object from the local datetime string
  // The datetime-local input provides a string in format "YYYY-MM-DDTHH:mm"
  // When we create a Date from this, it's interpreted as local time
  const localDate = new Date(localDateTimeString);
  
  // Return the UTC ISO string
  return localDate.toISOString();
}

/**
 * Convert a UTC ISO string to local datetime string for form display
 * @param utcISOString - UTC ISO string from API (e.g., "2024-01-15T19:30:00.000Z")
 * @returns Local datetime string for datetime-local input (e.g., "2024-01-15T14:30")
 */
export function utcToLocalDateTime(utcISOString: string): string {
  if (!utcISOString) return '';
  
  // Create a Date object from the UTC string
  const utcDate = new Date(utcISOString);
  
  // Get the local time components
  const year = utcDate.getFullYear();
  const month = String(utcDate.getMonth() + 1).padStart(2, '0');
  const day = String(utcDate.getDate()).padStart(2, '0');
  const hours = String(utcDate.getHours()).padStart(2, '0');
  const minutes = String(utcDate.getMinutes()).padStart(2, '0');
  
  // Return in datetime-local format
  return `${year}-${month}-${day}T${hours}:${minutes}`;
}

/**
 * Format a UTC ISO string for local display
 * @param utcISOString - UTC ISO string from API
 * @param options - Intl.DateTimeFormatOptions for formatting
 * @returns Formatted local time string
 */
export function formatLocalDateTime(
  utcISOString: string, 
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  }
): string {
  if (!utcISOString) return '';
  
  const date = new Date(utcISOString);
  return date.toLocaleDateString('en-US', options);
}

/**
 * Format a UTC ISO string for local time display only
 * @param utcISOString - UTC ISO string from API
 * @param options - Intl.DateTimeFormatOptions for time formatting
 * @returns Formatted local time string
 */
export function formatLocalTime(
  utcISOString: string,
  options: Intl.DateTimeFormatOptions = {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  }
): string {
  if (!utcISOString) return '';
  
  const date = new Date(utcISOString);
  return date.toLocaleTimeString('en-US', options);
}

/**
 * Format a UTC ISO string for local date display only
 * @param utcISOString - UTC ISO string from API
 * @param options - Intl.DateTimeFormatOptions for date formatting
 * @returns Formatted local date string
 */
export function formatLocalDate(
  utcISOString: string,
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }
): string {
  if (!utcISOString) return '';
  
  const date = new Date(utcISOString);
  return date.toLocaleDateString('en-US', options);
}

/**
 * Get the user's timezone
 * @returns The user's timezone identifier (e.g., "America/New_York")
 */
export function getUserTimezone(): string {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
}

/**
 * Get the user's timezone offset in minutes
 * @returns Timezone offset in minutes (negative for timezones ahead of UTC)
 */
export function getTimezoneOffset(): number {
  return new Date().getTimezoneOffset();
}

/**
 * Check if the current time is in daylight saving time
 * @returns True if currently in DST
 */
export function isDaylightSavingTime(): boolean {
  const now = new Date();
  const january = new Date(now.getFullYear(), 0, 1);
  const july = new Date(now.getFullYear(), 6, 1);
  
  return Math.max(january.getTimezoneOffset(), july.getTimezoneOffset()) !== now.getTimezoneOffset();
}
