<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Completion Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            text-align: center;
            max-width: 500px;
            margin: 1rem;
        }
        h1 {
            color: #1f2937;
            margin-bottom: 1rem;
            font-size: 2rem;
            font-weight: 700;
        }
        p {
            color: #6b7280;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        .btn {
            background: #3b82f6;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.2s;
        }
        .btn:hover {
            background: #2563eb;
        }
        .features {
            text-align: left;
            margin: 2rem 0;
            padding: 1rem;
            background: #f9fafb;
            border-radius: 8px;
        }
        .features h3 {
            color: #1f2937;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }
        .features ul {
            color: #6b7280;
            margin: 0;
            padding-left: 1.5rem;
        }
        .features li {
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Profile Completion Demo</h1>
        <p>
            Interactive demonstration of the Profile Completion feature with different provider scenarios.
            Test how the completion percentage is calculated and see the UI in action.
        </p>
        
        <div class="features">
            <h3>✨ What you'll see:</h3>
            <ul>
                <li>Real-time completion calculation</li>
                <li>Interactive progress visualization</li>
                <li>Different completion scenarios (0%, 65%, 100%)</li>
                <li>Detailed section breakdown</li>
                <li>Next steps recommendations</li>
            </ul>
        </div>
        
        <a href="/profile-completion-demo" class="btn">
            Launch Demo →
        </a>
        
        <p style="margin-top: 2rem; font-size: 0.9rem;">
            <a href="/" style="color: #6b7280; text-decoration: none;">← Back to Dashboard</a>
        </p>
    </div>
</body>
</html>
