import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { PaymentMethodsService } from '../services/payment-methods.service';
import { useUserLocation } from './useUserLocation';
import { useSubscriptionStatus } from './useSubscription';
import {
  PaymentMethodsResponse,
  PaymentMethod,
  ChargilyCheckoutResponse,
  CheckoutSessionRequest,
  PaymentProcessor,
  PaymentMethodType,
} from '../types';
import { ErrorLogger } from '../lib/error-utils';
import { parsePaymentError, logPaymentError, getUserErrorMessage } from '../utils/payment-errors.utils';
import {
  getAvailablePaymentMethodsWithCriteria,
  getRecommendedPaymentMethod as getRecommendedPaymentMethodWithCriteria,
  PaymentMethodCriteria
} from '../utils/payment-method-selection.utils';
import toast from 'react-hot-toast';

/**
 * Query keys for payment methods related queries
 */
export const paymentMethodsKeys = {
  all: ['payment-methods'] as const,
  methods: () => [...paymentMethodsKeys.all, 'methods'] as const,
  analytics: (filters?: any) => [...paymentMethodsKeys.all, 'analytics', filters] as const,
  paymentLinks: () => [...paymentMethodsKeys.all, 'payment-links'] as const,
};

/**
 * Hook for fetching available payment methods
 * Automatically filters based on user location
 */
export const usePaymentMethods = () => {
  const { location, isLoading: locationLoading } = useUserLocation();

  return useQuery({
    queryKey: paymentMethodsKeys.methods(),
    queryFn: () => PaymentMethodsService.getAvailablePaymentMethods(location?.countryCode),
    enabled: !locationLoading, // Don't wait for location, use fallback
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
    onError: (error: any) => {
      const paymentError = parsePaymentError(error, 'chargily');
      logPaymentError(paymentError, 'usePaymentMethods');
      ErrorLogger.log(error, { context: 'fetchPaymentMethods' });
    },
  });
};

/**
 * Hook for getting recommended payment method with enhanced criteria
 */
export const useRecommendedPaymentMethod = (additionalCriteria?: Partial<PaymentMethodCriteria>) => {
  const { data: paymentMethodsData, isLoading } = usePaymentMethods();
  const { location } = useUserLocation();
  const { data: subscriptionData } = useSubscriptionStatus();

  const criteria: PaymentMethodCriteria = {
    userLocation: location || {
      country: 'Unknown',
      countryCode: '',
      isAlgeria: false,
      detectionMethod: 'default',
      confidence: 'low',
    },
    subscriptionStatus: subscriptionData?.subscription?.status as any,
    previousPaymentMethod: subscriptionData?.subscription?.paymentProcessor as PaymentProcessor,
    hasFailedPayments: subscriptionData?.subscription?.status === 'past_due',
    isFirstTimeUser: !subscriptionData?.subscription,
    ...additionalCriteria,
  };

  const recommendation = paymentMethodsData?.data?.methods && location
    ? getRecommendedPaymentMethodWithCriteria(criteria, paymentMethodsData.data.methods)
    : null;

  return {
    recommendedMethod: recommendation?.primary || null,
    alternatives: recommendation?.alternatives || [],
    reasoning: recommendation?.reasoning || '',
    confidence: recommendation?.confidence || 'low',
    isLoading,
    isAlgeria: location?.isAlgeria || false,
    criteria,
  };
};

/**
 * Hook for creating Chargily checkout session
 */
export const useCreateChargilyCheckout = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CheckoutSessionRequest) => 
      PaymentMethodsService.createChargilyCheckout(data),
    onSuccess: (response: ChargilyCheckoutResponse) => {
      // Invalidate payment-related queries
      queryClient.invalidateQueries({ queryKey: paymentMethodsKeys.all });
      
      // Redirect to Chargily checkout
      if (response.success && response.data.checkoutUrl) {
        PaymentMethodsService.redirectToChargilyCheckout(response.data.checkoutUrl);
      }
    },
    onError: (error: any) => {
      const paymentError = parsePaymentError(error, 'chargily');
      logPaymentError(paymentError, 'createChargilyCheckout');
      toast.error(getUserErrorMessage(paymentError));
    },
  });
};

/**
 * Hook for fetching Chargily payment status
 */
export const useChargilyPaymentStatus = () => {
  return useQuery({
    queryKey: [...paymentMethodsKeys.all, 'chargily-status'],
    queryFn: () => PaymentMethodsService.getChargilyPaymentStatus(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    onError: (error: any) => {
      const paymentError = parsePaymentError(error, 'chargily');
      logPaymentError(paymentError, 'useChargilyPaymentStatus');
      ErrorLogger.log(error, { context: 'fetchChargilyPaymentStatus' });
    },
  });
};

/**
 * Hook for creating payment links
 */
export const useCreatePaymentLink = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: {
      name: string;
      description: string;
      planId: string;
      customAmount?: boolean;
      metadata?: Record<string, any>;
    }) => PaymentMethodsService.createPaymentLink(data),
    onSuccess: () => {
      // Invalidate payment links query
      queryClient.invalidateQueries({ queryKey: paymentMethodsKeys.paymentLinks() });
      toast.success('Payment link created successfully');
    },
    onError: (error: any) => {
      const paymentError = parsePaymentError(error, 'chargily');
      logPaymentError(paymentError, 'createPaymentLink');
      toast.error(getUserErrorMessage(paymentError));
    },
  });
};

/**
 * Hook for fetching payment links
 */
export const usePaymentLinks = () => {
  return useQuery({
    queryKey: paymentMethodsKeys.paymentLinks(),
    queryFn: () => PaymentMethodsService.getPaymentLinks(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    onError: (error: any) => {
      const paymentError = parsePaymentError(error, 'chargily');
      logPaymentError(paymentError, 'usePaymentLinks');
      ErrorLogger.log(error, { context: 'fetchPaymentLinks' });
    },
  });
};

/**
 * Hook for fetching payment analytics
 */
export const usePaymentAnalytics = (params?: {
  period?: 'daily' | 'weekly' | 'monthly' | 'yearly';
  startDate?: string;
  endDate?: string;
}) => {
  return useQuery({
    queryKey: paymentMethodsKeys.analytics(params),
    queryFn: () => PaymentMethodsService.getPaymentAnalytics(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
    onError: (error: any) => {
      const paymentError = parsePaymentError(error, 'chargily');
      logPaymentError(paymentError, 'usePaymentAnalytics');
      ErrorLogger.log(error, { context: 'fetchPaymentAnalytics' });
    },
  });
};

/**
 * Hook for creating Chargily customers
 */
export const useCreateChargilyCustomer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: {
      name: string;
      email: string;
      phone: string;
      address?: {
        country: string;
        state: string;
        address: string;
      };
      metadata?: Record<string, any>;
    }) => PaymentMethodsService.createChargilyCustomer(data),
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: paymentMethodsKeys.all });
      toast.success('Customer created successfully');
    },
    onError: (error: any) => {
      const paymentError = parsePaymentError(error, 'chargily');
      logPaymentError(paymentError, 'createChargilyCustomer');
      toast.error(getUserErrorMessage(paymentError));
    },
  });
};

/**
 * Hook for checking if Chargily is available
 */
export const useChargilyAvailability = () => {
  const { data: paymentMethodsData, isLoading } = usePaymentMethods();
  const { location } = useUserLocation();

  const isChargilyAvailable = paymentMethodsData?.data?.methods?.some(
    method => method.id === 'chargily' && method.isAvailable
  ) || false;

  const shouldRecommendChargily = location?.isAlgeria && isChargilyAvailable;

  return {
    isChargilyAvailable,
    shouldRecommendChargily,
    isLoading,
    userLocation: location,
  };
};

/**
 * Hook for validating payment method selection
 */
export const usePaymentMethodValidation = () => {
  const { data: paymentMethodsData } = usePaymentMethods();
  const { location } = useUserLocation();

  const validateSelection = (
    paymentProcessor: PaymentProcessor,
    paymentMethod: PaymentMethodType
  ): boolean => {
    return PaymentMethodsService.validatePaymentMethodSelection(
      paymentProcessor,
      paymentMethod,
      location?.countryCode
    );
  };

  const getPaymentMethodById = (methodId: string): PaymentMethod | undefined => {
    return paymentMethodsData?.data?.methods?.find(method => method.id === methodId);
  };

  return {
    validateSelection,
    getPaymentMethodById,
    availableMethods: paymentMethodsData?.data?.methods || [],
  };
};
