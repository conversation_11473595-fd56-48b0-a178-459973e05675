/**
 * Debug component to test profile completion API integration
 */

import React, { useEffect, useState } from 'react';
import { useAuth } from '../../context/AuthContext';
import { ProfileCompletionService } from '../../services/profile-completion.service';
import { ProviderService } from '../../services/provider.service';
import { ProfileCompletionResult } from '../../types/profile-completion';

const ProfileCompletionDebug: React.FC = () => {
  const { user, provider, isAuthenticated } = useAuth();
  const [completion, setCompletion] = useState<ProfileCompletionResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [apiResponse, setApiResponse] = useState<any>(null);
  const [rawApiTest, setRawApiTest] = useState<any>(null);

  const testApiCall = async () => {
    if (!isAuthenticated) {
      setError('Not authenticated');
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      console.log('Testing profile completion API...');
      console.log('User:', user);
      console.log('Provider:', provider);
      console.log('Is Authenticated:', isAuthenticated);

      // Test the API call
      const result = await ProviderService.getProfileCompletion();
      console.log('API Response:', result);

      setCompletion(result);
      setApiResponse(result);
    } catch (err: any) {
      console.error('API Error:', err);
      console.error('Error details:', {
        message: err.message,
        status: err.response?.status,
        statusText: err.response?.statusText,
        data: err.response?.data,
      });

      let errorMessage = err.message || 'Failed to fetch profile completion';
      if (err.response?.status === 401) {
        errorMessage = 'Authentication failed - please log in again';
      } else if (err.response?.status === 404) {
        errorMessage = 'Profile completion endpoint not found';
      } else if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const testRawApiCall = async () => {
    setLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('auth_token');
      console.log('Token:', token ? 'Present' : 'Missing');

      const response = await fetch('/api/auth/provider/profile-completion', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      console.log('Raw API Response Status:', response.status);
      console.log('Raw API Response Headers:', response.headers);

      const data = await response.json();
      console.log('Raw API Response Data:', data);

      setRawApiTest({ status: response.status, data });

      if (response.ok && data.success) {
        setCompletion(data.data);
        setApiResponse(data.data);
      } else {
        setError(`API returned error: ${data.message || 'Unknown error'}`);
      }
    } catch (err: any) {
      console.error('Raw API Error:', err);
      setError(`Raw API call failed: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated && user) {
      testApiCall();
    }
  }, [isAuthenticated, user]);

  if (!isAuthenticated) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <h3 className="text-yellow-800 font-medium mb-2">Authentication Required</h3>
        <p className="text-yellow-700 text-sm">
          Please log in to test the profile completion API.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Profile Completion API Debug
        </h3>
        <div className="space-x-2">
          <button
            onClick={testApiCall}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
          >
            {loading ? 'Testing...' : 'Test Service'}
          </button>
          <button
            onClick={testRawApiCall}
            disabled={loading}
            className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 disabled:opacity-50 transition-colors"
          >
            {loading ? 'Testing...' : 'Test Raw API'}
          </button>
        </div>
      </div>

      {/* User Info */}
      <div className="mb-4 p-3 bg-gray-50 rounded-md">
        <h4 className="font-medium text-gray-900 mb-2">Authentication Status</h4>
        <div className="text-sm space-y-1">
          <div>User ID: {user?.id || 'Not available'}</div>
          <div>Email: {user?.email || 'Not available'}</div>
          <div>Provider ID: {provider?.id || 'Not available'}</div>
          <div>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</div>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Testing API...</span>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
          <h4 className="text-red-800 font-medium mb-2">API Error</h4>
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}

      {/* Success State */}
      {completion && (
        <div className="space-y-4">
          <div className="bg-green-50 border border-green-200 rounded-md p-4">
            <h4 className="text-green-800 font-medium mb-2">✅ API Call Successful</h4>
            <div className="text-sm text-green-700">
              <div>Overall Percentage: {completion.overallPercentage}%</div>
              <div>Overall Completed: {completion.overallCompleted ? 'Yes' : 'No'}</div>
              <div>Next Steps: {completion.nextSteps.length}</div>
              <div>Critical Missing: {completion.criticalMissing.length}</div>
            </div>
          </div>

          {/* Breakdown Details */}
          <div className="bg-gray-50 rounded-md p-4">
            <h4 className="font-medium text-gray-900 mb-3">Section Breakdown</h4>
            <div className="space-y-2 text-sm">
              {Object.entries(completion.breakdown).map(([section, data]) => (
                <div key={section} className="flex justify-between items-center">
                  <span className="capitalize">{section.replace(/([A-Z])/g, ' $1').trim()}:</span>
                  <span className={`font-medium ${data.completed ? 'text-green-600' : 'text-red-600'}`}>
                    {data.percentage}% {data.completed ? '✓' : '✗'}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Next Steps */}
          {completion.nextSteps.length > 0 && (
            <div className="bg-blue-50 rounded-md p-4">
              <h4 className="font-medium text-blue-900 mb-2">Next Steps</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                {completion.nextSteps.map((step, index) => (
                  <li key={index}>• {step}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Raw API Response */}
          <details className="bg-gray-50 rounded-md p-4">
            <summary className="font-medium text-gray-900 cursor-pointer">
              Service API Response
            </summary>
            <pre className="mt-2 text-xs text-gray-700 overflow-x-auto">
              {JSON.stringify(apiResponse, null, 2)}
            </pre>
          </details>

          {/* Raw API Test Results */}
          {rawApiTest && (
            <details className="bg-gray-50 rounded-md p-4">
              <summary className="font-medium text-gray-900 cursor-pointer">
                Raw API Test Results
              </summary>
              <div className="mt-2 text-xs text-gray-700">
                <div className="mb-2">Status: {rawApiTest.status}</div>
                <pre className="overflow-x-auto">
                  {JSON.stringify(rawApiTest.data, null, 2)}
                </pre>
              </div>
            </details>
          )}
        </div>
      )}

      {/* API Endpoint Info */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <h4 className="font-medium text-gray-900 mb-2">API Information</h4>
        <div className="text-sm text-gray-600 space-y-1">
          <div>Endpoint: GET /api/auth/provider/profile-completion</div>
          <div>Authentication: Bearer token required</div>
          <div>Expected Response: ProfileCompletionResponse</div>
        </div>
      </div>
    </div>
  );
};

export default ProfileCompletionDebug;
