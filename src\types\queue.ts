/**
 * Queue related types - Updated to match API documentation
 */

import { Provider, Location, Service, Appointment, User } from './index';

export interface Queue {
  id: number;
  sProviderId?: number;
  sProvidingPlaceId: number;
  title: string;
  isActive: boolean;
  maxCapacity?: number;
  estimatedWaitTime?: number; // in minutes
  currentPosition?: number;
  createdAt: string;
  updatedAt: string;

  // Relations
  provider?: Provider;
  location?: Location;
  sProvidingPlace?: Location; // API uses this name
  services?: Service[];
  openings?: QueueOpening[];
  appointments?: Appointment[];
  queueServices?: QueueService[];
  swapRequests?: QueueSwapRequest[];
}

export interface QueueOpening {
  id: number;
  queueId: number;
  dayOfWeek: string; // "Monday", "Tuesday", etc.
  type: string; // "regular", "exception", "holiday"
  isActive: boolean;
  createdAt: string;
  updatedAt: string;

  // Time intervals for this opening
  hours: QueueOpeningHours[];

  // Relations
  queue?: Queue;
}

export interface QueueOpeningHours {
  id: number;
  queueOpeningId: number;
  timeFrom: string; // HH:mm format
  timeTo: string;   // HH:mm format
  createdAt: string;
  updatedAt: string;

  // Relations
  queueOpening?: QueueOpening;
}

export interface QueueService {
  id: number;
  queueId: number;
  serviceId: number;
  isActive: boolean;
  priority?: number;
  estimatedDuration?: number; // in minutes
  createdAt: string;
  updatedAt: string;
  
  // Relations
  queue?: Queue;
  service?: Service;
}

export interface QueueSwapRequest {
  id: number;
  queueId: number;
  requestingCustomerId: string;
  targetCustomerId: string;
  requestingPosition: number;
  targetPosition: number;
  reason?: string;
  status: 'pending' | 'approved' | 'rejected' | 'expired';
  requestedAt: string;
  respondedAt?: string;
  expiresAt: string;
  
  // Relations
  queue?: Queue;
  requestingCustomer?: User;
  targetCustomer?: User;
}

export interface QueueCreateRequest {
  title: string;
  sProvidingPlaceId: number;
  isActive?: boolean;
  serviceIds: number[]; // At least one service must be assigned
  openingHours?: QueueOpeningCreateRequest[];
}

export interface QueueOpeningCreateRequest {
  dayOfWeek: string; // "Monday", "Tuesday", etc.
  isActive?: boolean;
  hours: QueueTimeSlot[];
}

export interface QueueTimeSlot {
  timeFrom: string; // HH:mm format
  timeTo: string;   // HH:mm format
}

export interface QueueUpdateRequest extends Partial<QueueCreateRequest> {
  id: number;
}

export interface QueueServiceCreateRequest {
  queueId: number;
  serviceId: number;
  isActive?: boolean;
  priority?: number;
  estimatedDuration?: number;
}

export interface QueueSwapCreateRequest {
  queueId: number;
  targetCustomerId: string;
  reason?: string;
}

export interface QueueSwapResponse {
  swapRequestId: number;
  status: 'approved' | 'rejected';
  reason?: string;
}

export interface QueueStatus {
  queueId: number;
  isActive: boolean;
  currentCapacity: number;
  maxCapacity?: number;
  estimatedWaitTime: number;
  nextAvailableSlot?: string;
  currentCustomers: QueueCustomer[];
}

export interface QueueCustomer {
  customerId: string;
  customerName: string;
  position: number;
  estimatedWaitTime: number;
  serviceRequested?: string;
  joinedAt: string;
  appointmentId?: number;
}

export interface QueueFilters {
  locationId?: number;
  isActive?: boolean;
  serviceId?: number;
  hasCapacity?: boolean;
  search?: string;
}

export interface QueueStats {
  totalQueues: number;
  activeQueues: number;
  totalCustomersToday: number;
  averageWaitTime: number;
  busyQueues: Queue[];
  queueEfficiency: number;
}

// Queue limits and capacity management
export interface QueueLimits {
  currentCount: number;
  maxAllowed: number;
  canCreate: boolean;
  upgradeRequired: boolean;
  tier: string;
  features: {
    realTimeUpdates: boolean;
    advancedAnalytics: boolean;
    prioritySupport: boolean;
  };
}

export interface QueueCapacity {
  queueId: number;
  date: string;
  totalMinutes: number;
  existingAppointments: number;
  availableMinutes: number;
  serviceCapacities: ServiceCapacity[];
}

export interface ServiceCapacity {
  serviceId: number;
  serviceName: string;
  duration: number;
  maxAppointments: number;
  totalMinutes: number;
}

// Real-time queue state
export interface QueueStateUpdate {
  queueId: number;
  position: number | null;
  estimatedStartTime: string | null;
  estimatedWaitMinutes: number | null;
  error?: string;
}
