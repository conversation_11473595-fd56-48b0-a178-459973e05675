/**
 * Customer related types
 */

import { Provider, User, Appointment } from './index';

export interface CustomerFolder {
  id: number;
  sProviderId: number;
  userId: string;
  notes?: string;
  isActive: boolean;
  tags?: string[];
  lastVisit?: string;
  totalVisits: number;
  totalSpent: number;
  preferredServices?: number[]; // Service IDs
  createdAt: string;
  updatedAt: string;
  
  // Relations
  provider?: Provider;
  customer?: User;
  appointments?: Appointment[];
  communications?: CustomerCommunication[];
}

export interface CustomerCommunication {
  id: number;
  customerFolderId: number;
  type: 'note' | 'call' | 'email' | 'sms' | 'in_person';
  subject?: string;
  content: string;
  direction: 'inbound' | 'outbound';
  createdByUserId: string;
  createdAt: string;
  
  // Relations
  customerFolder?: CustomerFolder;
  createdBy?: User;
}

export interface Customer extends User {
  // Additional customer-specific fields
  phone?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  address?: CustomerAddress;
  preferences?: CustomerPreferences;
  loyaltyPoints?: number;
  membershipLevel?: 'bronze' | 'silver' | 'gold' | 'platinum';
  
  // Provider-specific relationship data
  folder?: CustomerFolder;
}

export interface CustomerAddress {
  street?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
}

export interface CustomerPreferences {
  preferredContactMethod: 'email' | 'phone' | 'sms';
  marketingOptIn: boolean;
  reminderOptIn: boolean;
  preferredLanguage?: string;
  timezone?: string;
}

export interface CustomerCreateRequest {
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  address?: CustomerAddress;
  preferences?: CustomerPreferences;
  notes?: string;
  tags?: string[];
}

export interface CustomerUpdateRequest extends Partial<CustomerCreateRequest> {
  customerId: string;
}

export interface CustomerFolderUpdateRequest {
  customerId: string;
  notes?: string;
  tags?: string[];
  preferredServices?: number[];
  isActive?: boolean;
}

export interface CustomerCommunicationCreateRequest {
  customerId: string;
  type: 'note' | 'call' | 'email' | 'sms' | 'in_person';
  subject?: string;
  content: string;
  direction: 'inbound' | 'outbound';
}

export interface CustomerFilters {
  search?: string;
  isActive?: boolean;
  tags?: string[];
  membershipLevel?: 'bronze' | 'silver' | 'gold' | 'platinum';
  lastVisitFrom?: string;
  lastVisitTo?: string;
  totalVisitsMin?: number;
  totalVisitsMax?: number;
  totalSpentMin?: number;
  totalSpentMax?: number;
  preferredServices?: number[];
}

export interface CustomerStats {
  totalCustomers: number;
  activeCustomers: number;
  newCustomersThisMonth: number;
  returningCustomers: number;
  averageVisitsPerCustomer: number;
  averageSpendPerCustomer: number;
  topCustomers: Customer[];
  customerRetentionRate: number;
  membershipDistribution: {
    bronze: number;
    silver: number;
    gold: number;
    platinum: number;
  };
}

export interface CustomerHistory {
  appointments: Appointment[];
  communications: CustomerCommunication[];
  totalVisits: number;
  totalSpent: number;
  averageRating?: number;
  lastVisit?: string;
  firstVisit?: string;
  favoriteServices: {
    serviceId: number;
    serviceName: string;
    count: number;
  }[];
}

// Additional CRM Types
export interface CustomerSegment {
  id: number;
  name: string;
  description?: string;
  criteria: CustomerSegmentCriteria;
  customerCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface CustomerSegmentCriteria {
  totalSpentMin?: number;
  totalSpentMax?: number;
  totalVisitsMin?: number;
  totalVisitsMax?: number;
  lastVisitDaysAgo?: number;
  membershipLevels?: string[];
  tags?: string[];
  services?: number[];
}

export interface CustomerLifecycleStage {
  stage: 'prospect' | 'new' | 'active' | 'at_risk' | 'inactive' | 'churned';
  description: string;
  daysInStage: number;
  nextAction?: string;
}

export interface CustomerInsights {
  customerId: string;
  lifeCycleStage: CustomerLifecycleStage;
  riskScore: number; // 0-100, higher = more at risk
  valueScore: number; // 0-100, higher = more valuable
  engagementScore: number; // 0-100, higher = more engaged
  recommendations: CustomerRecommendation[];
  trends: {
    spendingTrend: 'increasing' | 'stable' | 'decreasing';
    visitFrequencyTrend: 'increasing' | 'stable' | 'decreasing';
    satisfactionTrend: 'improving' | 'stable' | 'declining';
  };
}

export interface CustomerRecommendation {
  type: 'service_suggestion' | 'retention_action' | 'upsell_opportunity' | 'follow_up';
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  estimatedImpact: string;
  actionRequired?: string;
}
