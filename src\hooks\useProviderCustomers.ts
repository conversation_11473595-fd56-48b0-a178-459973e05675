import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { ProviderCustomerService } from '../services/provider-customer.service';
import {
  ProviderCustomer,
  ProviderCustomerCreateRequest,
  ProviderCustomerUpdateRequest,
  ProviderCustomerFilters,
  ProviderCustomerResponse,
  CustomerFolderDetails,
} from '../types/provider-customer';
import { ErrorLogger } from '../lib/error-utils';

/**
 * Query keys for provider customers
 */
export const providerCustomerKeys = {
  all: ['provider-customers'] as const,
  lists: () => [...providerCustomerKeys.all, 'list'] as const,
  list: (filters?: ProviderCustomerFilters) => [...providerCustomerKeys.lists(), filters] as const,
  details: () => [...providerCustomerKeys.all, 'detail'] as const,
  detail: (id: string) => [...providerCustomerKeys.details(), id] as const,
  search: (query: string) => [...providerCustomerKeys.all, 'search', query] as const,
};

/**
 * Hook for fetching provider customers with filtering and pagination
 */
export const useProviderCustomers = (filters?: ProviderCustomerFilters) => {
  return useQuery({
    queryKey: providerCustomerKeys.list(filters),
    queryFn: () => ProviderCustomerService.getProviderCustomers(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

/**
 * Hook for fetching a specific provider customer
 */
export const useProviderCustomer = (customerId: string) => {
  return useQuery({
    queryKey: providerCustomerKeys.detail(customerId),
    queryFn: () => ProviderCustomerService.getProviderCustomer(customerId),
    enabled: !!customerId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

/**
 * Hook for searching provider customers
 */
export const useSearchProviderCustomers = (query: string) => {
  return useQuery({
    queryKey: providerCustomerKeys.search(query),
    queryFn: () => ProviderCustomerService.searchProviderCustomers(query),
    enabled: query.length >= 2, // Only search when query is at least 2 characters
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 1,
  });
};

/**
 * Hook for creating a provider customer
 */
export const useCreateProviderCustomer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ProviderCustomerCreateRequest) => 
      ProviderCustomerService.createProviderCustomer(data),
    onSuccess: (newCustomer) => {
      // Invalidate and refetch customers list
      queryClient.invalidateQueries({ queryKey: providerCustomerKeys.lists() });
      
      // Add the new customer to the cache
      queryClient.setQueryData(
        providerCustomerKeys.detail(newCustomer.id),
        newCustomer
      );
      
      toast.success('Customer created successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || error?.message || 'Failed to create customer';
      ErrorLogger.log(error, { context: 'createProviderCustomer' });
      toast.error(message);
    },
  });
};

/**
 * Hook for updating a provider customer
 */
export const useUpdateProviderCustomer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ProviderCustomerUpdateRequest) => 
      ProviderCustomerService.updateProviderCustomer(data),
    onSuccess: (updatedCustomer, variables) => {
      // Invalidate and refetch customers list
      queryClient.invalidateQueries({ queryKey: providerCustomerKeys.lists() });
      
      // Update the specific customer in cache
      queryClient.setQueryData(
        providerCustomerKeys.detail(variables.customerUserId),
        updatedCustomer
      );
      
      toast.success('Customer updated successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || error?.message || 'Failed to update customer';
      ErrorLogger.log(error, { context: 'updateProviderCustomer' });
      toast.error(message);
    },
  });
};

/**
 * Hook for deactivating a customer relationship
 */
export const useDeactivateCustomerRelationship = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (customerId: string) => 
      ProviderCustomerService.deactivateCustomerRelationship(customerId),
    onSuccess: (result, customerId) => {
      // Invalidate and refetch customers list
      queryClient.invalidateQueries({ queryKey: providerCustomerKeys.lists() });
      
      // Remove the customer from cache
      queryClient.removeQueries({ queryKey: providerCustomerKeys.detail(customerId) });
      
      toast.success(result.message || 'Customer relationship deactivated successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || error?.message || 'Failed to deactivate customer relationship';
      ErrorLogger.log(error, { context: 'deactivateCustomerRelationship' });
      toast.error(message);
    },
  });
};

/**
 * Hook for optimistic updates when creating customers
 */
export const useOptimisticCreateCustomer = () => {
  const queryClient = useQueryClient();
  const createMutation = useCreateProviderCustomer();

  const createWithOptimisticUpdate = async (data: ProviderCustomerCreateRequest) => {
    // Create optimistic customer
    const optimisticCustomer: ProviderCustomer = {
      id: `temp-${Date.now()}`,
      firstName: data.firstName,
      lastName: data.lastName,
      mobileNumber: data.mobileNumber,
      email: data.email,
      nationalId: data.nationalId,
      notes: data.notes,
      appointmentCount: 0,
      createdAt: new Date().toISOString(),
      isActive: true,
    };

    // Optimistically update the cache
    queryClient.setQueryData(
      providerCustomerKeys.list(),
      (old: ProviderCustomerResponse | undefined) => {
        if (!old) return { customers: [optimisticCustomer] };
        return {
          ...old,
          customers: [optimisticCustomer, ...old.customers],
        };
      }
    );

    try {
      // Perform the actual mutation
      const result = await createMutation.mutateAsync(data);
      
      // Replace optimistic data with real data
      queryClient.setQueryData(
        providerCustomerKeys.list(),
        (old: ProviderCustomerResponse | undefined) => {
          if (!old) return { customers: [result] };
          return {
            ...old,
            customers: old.customers.map(customer =>
              customer.id === optimisticCustomer.id ? result : customer
            ),
          };
        }
      );

      return result;
    } catch (error) {
      // Revert optimistic update on error
      queryClient.setQueryData(
        providerCustomerKeys.list(),
        (old: ProviderCustomerResponse | undefined) => {
          if (!old) return { customers: [] };
          return {
            ...old,
            customers: old.customers.filter(customer => customer.id !== optimisticCustomer.id),
          };
        }
      );
      throw error;
    }
  };

  return {
    ...createMutation,
    mutateAsync: createWithOptimisticUpdate,
  };
};

/**
 * Hook for prefetching customer data
 */
export const usePrefetchProviderCustomer = () => {
  const queryClient = useQueryClient();

  return (customerId: string) => {
    queryClient.prefetchQuery({
      queryKey: providerCustomerKeys.detail(customerId),
      queryFn: () => ProviderCustomerService.getProviderCustomer(customerId),
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };
};
