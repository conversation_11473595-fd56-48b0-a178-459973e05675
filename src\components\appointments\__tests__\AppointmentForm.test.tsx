import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import AppointmentForm from '../AppointmentForm';
import { useCreateAppointment, useUpdateAppointment } from '../../../hooks/useAppointments';
import { useServices } from '../../../hooks/useServices';
import { useLocations } from '../../../hooks/useLocations';
import { useQueues } from '../../../hooks/useQueues';
import { useSearchCustomers, useCreateCustomer } from '../../../hooks/useCustomers';

// Mock hooks
vi.mock('../../../hooks/useAppointments');
vi.mock('../../../hooks/useServices');
vi.mock('../../../hooks/useLocations');
vi.mock('../../../hooks/useQueues');
vi.mock('../../../hooks/useCustomers');

// Mock react-hot-toast
vi.mock('react-hot-toast', () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
  },
}));

const mockServices = [
  { id: 1, title: 'General Consultation', duration: 30, price: 100 },
  { id: 2, title: 'Follow-up', duration: 15, price: 50 },
];

const mockLocations = [
  { id: 1, name: 'Main Clinic' },
  { id: 2, name: 'Branch Office' },
];

const mockQueues = [
  { id: 1, title: 'General Queue', isActive: true, sProvidingPlaceId: 1 },
  { id: 2, title: 'Priority Queue', isActive: true, sProvidingPlaceId: 1 },
];

const mockCustomers = [
  { id: 'customer-1', firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
  { id: 'customer-2', firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>' },
];

const renderWithQueryClient = (component: React.ReactElement) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('AppointmentForm', () => {
  const mockOnClose = vi.fn();
  const mockOnSuccess = vi.fn();
  const mockCreateMutation = {
    mutateAsync: vi.fn(),
    isPending: false,
    error: null,
  };
  const mockUpdateMutation = {
    mutateAsync: vi.fn(),
    isPending: false,
    error: null,
  };
  const mockCreateCustomerMutation = {
    mutateAsync: vi.fn(),
    isPending: false,
    error: null,
  };

  beforeEach(() => {
    vi.clearAllMocks();

    (useCreateAppointment as Mock).mockReturnValue(mockCreateMutation);
    (useUpdateAppointment as Mock).mockReturnValue(mockUpdateMutation);
    (useCreateCustomer as Mock).mockReturnValue(mockCreateCustomerMutation);
    (useServices as Mock).mockReturnValue({ data: mockServices });
    (useLocations as Mock).mockReturnValue({ data: mockLocations });
    (useQueues as Mock).mockReturnValue({ data: mockQueues });
    (useSearchCustomers as Mock).mockReturnValue({ data: mockCustomers });
  });

  describe('Form Rendering', () => {
    it('should render new appointment form correctly', () => {
      renderWithQueryClient(
        <AppointmentForm onClose={mockOnClose} onSuccess={mockOnSuccess} />
      );

      expect(screen.getByText('New Appointment')).toBeInTheDocument();
      expect(screen.getByText('Schedule a new appointment')).toBeInTheDocument();
      expect(screen.getByLabelText(/Customer/)).toBeInTheDocument();
      expect(screen.getByLabelText(/Service/)).toBeInTheDocument();
      expect(screen.getByLabelText(/Location/)).toBeInTheDocument();
      expect(screen.getByLabelText(/Queue/)).toBeInTheDocument();
      expect(screen.getByLabelText(/Start Time/)).toBeInTheDocument();
      expect(screen.getByLabelText(/End Time/)).toBeInTheDocument();
      expect(screen.getByLabelText(/Duration/)).toBeInTheDocument();
      expect(screen.getByLabelText(/Slots/)).toBeInTheDocument();
    });

    it('should render edit appointment form correctly', () => {
      const mockAppointment = {
        id: 1,
        customerUserId: 'customer-1',
        serviceId: 1,
        placeId: 1,
        queueId: 1,
        status: 'confirmed' as const,
        expectedAppointmentStartTime: '2024-01-15T10:00:00Z',
        expectedAppointmentEndTime: '2024-01-15T10:30:00Z',
        serviceDuration: 30,
        notes: 'Test appointment',
        slots: 1,
        customer: mockCustomers[0],
        service: mockServices[0],
        place: mockLocations[0],
      };

      renderWithQueryClient(
        <AppointmentForm
          appointment={mockAppointment}
          onClose={mockOnClose}
          onSuccess={mockOnSuccess}
        />
      );

      expect(screen.getByText('Edit Appointment')).toBeInTheDocument();
      expect(screen.getByText('Update appointment details')).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    it('should show validation errors for required fields', async () => {
      const user = userEvent.setup();

      renderWithQueryClient(
        <AppointmentForm onClose={mockOnClose} onSuccess={mockOnSuccess} />
      );

      const submitButton = screen.getByRole('button', { name: /Create Appointment/ });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Please select a customer')).toBeInTheDocument();
        expect(screen.getByText('Please select a service')).toBeInTheDocument();
        expect(screen.getByText('Please select a location')).toBeInTheDocument();
        expect(screen.getByText('Please select a queue')).toBeInTheDocument();
      });
    });

    it('should validate time fields correctly', async () => {
      const user = userEvent.setup();

      renderWithQueryClient(
        <AppointmentForm onClose={mockOnClose} onSuccess={mockOnSuccess} />
      );

      const submitButton = screen.getByRole('button', { name: /Create Appointment/ });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Please select start time')).toBeInTheDocument();
        expect(screen.getByText('End time is required')).toBeInTheDocument();
      });
    });

    it('should validate duration and slots', async () => {
      const user = userEvent.setup();

      renderWithQueryClient(
        <AppointmentForm onClose={mockOnClose} onSuccess={mockOnSuccess} />
      );

      // Clear duration field and enter invalid value
      const durationField = screen.getByLabelText(/Duration/);
      await user.clear(durationField);
      await user.type(durationField, '0');

      // Clear slots field and enter invalid value
      const slotsField = screen.getByLabelText(/Slots/);
      await user.clear(slotsField);
      await user.type(slotsField, '0');

      const submitButton = screen.getByRole('button', { name: /Create Appointment/ });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Service duration is required')).toBeInTheDocument();
        expect(screen.getByText('At least 1 slot is required')).toBeInTheDocument();
      });
    });
  });

  describe('Customer Selection', () => {
    it('should allow customer search and selection', async () => {
      const user = userEvent.setup();

      renderWithQueryClient(
        <AppointmentForm onClose={mockOnClose} onSuccess={mockOnSuccess} />
      );

      const searchInput = screen.getByPlaceholderText('Search customers...');
      await user.type(searchInput, 'John');

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });

      // Click on customer to select
      await user.click(screen.getByText('John Doe'));

      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Change' })).toBeInTheDocument();
    });

    it('should show new customer form when requested', async () => {
      const user = userEvent.setup();

      renderWithQueryClient(
        <AppointmentForm onClose={mockOnClose} onSuccess={mockOnSuccess} />
      );

      const newCustomerButton = screen.getByRole('button', { name: 'Create New Customer' });
      await user.click(newCustomerButton);

      expect(screen.getByText('New Customer Information')).toBeInTheDocument();
      expect(screen.getByLabelText(/First Name/)).toBeInTheDocument();
      expect(screen.getByLabelText(/Last Name/)).toBeInTheDocument();
      expect(screen.getByLabelText(/Email/)).toBeInTheDocument();
      expect(screen.getByLabelText(/Phone/)).toBeInTheDocument();
    });
  });

  describe('Form Submission', () => {
    it('should create appointment successfully', async () => {
      const user = userEvent.setup();
      mockCreateMutation.mutateAsync.mockResolvedValue({ id: 1 });

      renderWithQueryClient(
        <AppointmentForm onClose={mockOnClose} onSuccess={mockOnSuccess} />
      );

      // Fill out the form
      const searchInput = screen.getByPlaceholderText('Search customers...');
      await user.type(searchInput, 'John');
      await user.click(screen.getByText('John Doe'));

      const serviceSelect = screen.getByLabelText(/Service/);
      await user.selectOptions(serviceSelect, '1');

      const locationSelect = screen.getByLabelText(/Location/);
      await user.selectOptions(locationSelect, '1');

      const queueSelect = screen.getByLabelText(/Queue/);
      await user.selectOptions(queueSelect, '1');

      const startTimeInput = screen.getByLabelText(/Start Time/);
      await user.type(startTimeInput, '2024-01-15T10:00');

      const endTimeInput = screen.getByLabelText(/End Time/);
      await user.type(endTimeInput, '2024-01-15T10:30');

      const submitButton = screen.getByRole('button', { name: /Create Appointment/ });
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockCreateMutation.mutateAsync).toHaveBeenCalledWith({
          customerUserId: 'customer-1',
          serviceId: 1,
          placeId: 1,
          queueId: 1,
          expectedAppointmentStartTime: '2024-01-15T10:00',
          expectedAppointmentEndTime: '2024-01-15T10:30',
          serviceDuration: 30,
          notes: '',
          slots: 1,
        });
        expect(mockOnSuccess).toHaveBeenCalled();
      });
    });

    it('should handle form submission errors', async () => {
      const user = userEvent.setup();
      const error = new Error('Failed to create appointment');
      mockCreateMutation.mutateAsync.mockRejectedValue(error);
      mockCreateMutation.error = error;

      renderWithQueryClient(
        <AppointmentForm onClose={mockOnClose} onSuccess={mockOnSuccess} />
      );

      // Fill out minimal required fields
      const searchInput = screen.getByPlaceholderText('Search customers...');
      await user.type(searchInput, 'John');
      await user.click(screen.getByText('John Doe'));

      const submitButton = screen.getByRole('button', { name: /Create Appointment/ });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Failed to create appointment')).toBeInTheDocument();
      });
    });
  });

  describe('Date Handling', () => {
    it('should set initial date when selectedDate is provided', () => {
      const selectedDate = '2024-01-15';

      renderWithQueryClient(
        <AppointmentForm
          selectedDate={selectedDate}
          onClose={mockOnClose}
          onSuccess={mockOnSuccess}
        />
      );

      const startTimeInput = screen.getByLabelText(/Start Time/) as HTMLInputElement;
      expect(startTimeInput.value).toBe('2024-01-15T09:00');

      const endTimeInput = screen.getByLabelText(/End Time/) as HTMLInputElement;
      expect(endTimeInput.value).toBe('2024-01-15T09:30');
    });
  });
});
