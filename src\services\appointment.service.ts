import { apiClient } from '../lib/api-client';
import { config } from '../lib/config';
import {
  Appointment,
  AppointmentCreateRequest,
  AppointmentUpdateRequest,
  AppointmentFilters,
  AppointmentStats,
  AppointmentStatusUpdate,
  AppointmentTimeSlot,
  Customer,
} from '../types';

/**
 * Appointment management API service
 */
export class AppointmentService {
  /**
   * Get all appointments for the provider
   */
  static async getAppointments(filters?: AppointmentFilters): Promise<Appointment[]> {
    const response = await apiClient.get<{
      success: boolean;
      data: {
        appointments: Appointment[];
        pagination: {
          page: number;
          limit: number;
          total: number;
          totalPages: number;
          hasNext: boolean;
          hasPrev: boolean;
        };
      };
      message: string;
    }>(
      config.endpoints.appointments.base,
      { params: filters }
    );
    return response.data.data.appointments;
  }

  /**
   * Get appointment by ID
   */
  static async getAppointment(id: number): Promise<Appointment> {
    const response = await apiClient.get<{
      success: boolean;
      data: Appointment;
      message: string;
    }>(`${config.endpoints.appointments.base}/${id}`);
    return response.data.data;
  }

  /**
   * Create a new appointment
   */
  static async createAppointment(data: AppointmentCreateRequest): Promise<Appointment> {
    const response = await apiClient.post<{
      success: boolean;
      data: Appointment;
      message: string;
    }>(
      config.endpoints.appointments.base,
      data
    );
    return response.data.data;
  }

  /**
   * Update an existing appointment
   */
  static async updateAppointment(id: number, data: AppointmentUpdateRequest): Promise<Appointment> {
    const response = await apiClient.put<{
      success: boolean;
      data: Appointment;
      message: string;
    }>(
      `${config.endpoints.appointments.base}/${id}`,
      data
    );
    return response.data.data;
  }

  /**
   * Delete an appointment
   */
  static async deleteAppointment(id: number): Promise<void> {
    const response = await apiClient.delete<{
      success: boolean;
      message: string;
    }>(`${config.endpoints.appointments.base}/${id}`);

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to delete appointment');
    }
  }

  /**
   * Update appointment status
   */
  static async updateAppointmentStatus(id: number, statusUpdate: AppointmentStatusUpdate): Promise<Appointment> {
    // Prepare the payload according to API specification
    const payload = {
      status: statusUpdate.status,
      notes: statusUpdate.notes || statusUpdate.changeReason // Use notes if provided, fallback to changeReason for backward compatibility
    };

    const response = await apiClient.put<{
      success: boolean;
      data: Appointment;
      message: string;
    }>(
      `${config.endpoints.appointments.base}/${id}/status`,
      payload
    );
    return response.data.data;
  }

  /**
   * Get appointment statistics
   */
  static async getAppointmentStats(filters?: { startDate?: string; endDate?: string }): Promise<AppointmentStats> {
    const response = await apiClient.get<{
      success: boolean;
      data: AppointmentStats;
      message: string;
    }>(
      `${config.endpoints.appointments.base}/stats`,
      { params: filters }
    );
    return response.data.data;
  }

  /**
   * Get available time slots for a service
   */
  static async getAvailableTimeSlots(params: {
    serviceId: number;
    locationId: number;
    date: string;
  }): Promise<AppointmentTimeSlot[]> {
    const response = await apiClient.get<{
      success: boolean;
      data: AppointmentTimeSlot[];
      message: string;
    }>(
      `${config.endpoints.appointments.base}/available-slots`,
      { params }
    );
    return response.data.data;
  }

  /**
   * Validate appointment creation (business rules)
   */
  static async validateAppointmentCreation(data: AppointmentCreateRequest): Promise<{
    isValid: boolean;
    errors: string[];
  }> {
    const errors: string[] = [];

    // Check provider credits
    try {
      const response = await apiClient.get<{
        success: boolean;
        data: { credits: number };
        message: string;
      }>('/api/auth/providers/credits');

      if (response.data.data.credits < 1) {
        errors.push('Insufficient credits. At least 1 credit is required to create an appointment.');
      }
    } catch (error) {
      errors.push('Unable to verify provider credits.');
    }

    // Check for time slot conflicts
    try {
      const conflictResponse = await apiClient.post<{
        success: boolean;
        data: { hasConflict: boolean; conflictingAppointments: Appointment[] };
        message: string;
      }>(`${config.endpoints.appointments.base}/check-conflicts`, {
        queueId: data.queueId,
        startTime: data.expectedAppointmentStartTime,
        endTime: data.expectedAppointmentEndTime,
      });

      if (conflictResponse.data.data.hasConflict) {
        errors.push('Time slot conflict detected. Please choose a different time.');
      }
    } catch (error) {
      errors.push('Unable to verify time slot availability.');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validate status transition
   */
  static validateStatusTransition(currentStatus: AppointmentStatus, newStatus: AppointmentStatus): {
    isValid: boolean;
    error?: string;
  } {
    const validTransitions: Record<AppointmentStatus, AppointmentStatus[]> = {
      pending: ['confirmed', 'canceled'],
      confirmed: ['InProgress', 'canceled', 'noshow'],
      InProgress: ['completed', 'canceled'],
      completed: [], // Terminal state
      canceled: [], // Terminal state
      noshow: [], // Terminal state
    };

    const allowedTransitions = validTransitions[currentStatus] || [];

    if (!allowedTransitions.includes(newStatus)) {
      return {
        isValid: false,
        error: `Invalid status transition from ${currentStatus} to ${newStatus}`,
      };
    }

    return { isValid: true };
  }

  /**
   * Confirm an appointment
   */
  static async confirmAppointment(id: number): Promise<Appointment> {
    const response = await apiClient.post<Appointment>(
      `${config.endpoints.appointments.base}/${id}/confirm`
    );
    return response.data;
  }

  /**
   * Cancel an appointment
   */
  static async cancelAppointment(id: number, reason?: string): Promise<Appointment> {
    const response = await apiClient.post<Appointment>(
      `${config.endpoints.appointments.base}/${id}/cancel`,
      { reason }
    );
    return response.data;
  }

  /**
   * Complete an appointment
   */
  static async completeAppointment(id: number, notes?: string): Promise<Appointment> {
    const response = await apiClient.post<Appointment>(
      `${config.endpoints.appointments.base}/${id}/complete`,
      { notes }
    );
    return response.data;
  }

  /**
   * Reschedule an appointment
   */
  static async rescheduleAppointment(id: number, newDateTime: string): Promise<Appointment> {
    const response = await apiClient.post<Appointment>(
      `${config.endpoints.appointments.base}/${id}/reschedule`,
      { newDateTime }
    );
    return response.data;
  }

  /**
   * Get appointments for a specific date range
   */
  static async getAppointmentsByDateRange(startDate: string, endDate: string): Promise<Appointment[]> {
    const response = await apiClient.get<Appointment[]>(
      `${config.endpoints.appointments.base}/date-range`,
      { params: { startDate, endDate } }
    );
    return response.data;
  }

  /**
   * Get customer information
   */
  static async getCustomer(id: number): Promise<Customer> {
    const response = await apiClient.get<Customer>(
      `${config.endpoints.customers.base}/${id}`
    );
    return response.data;
  }

  /**
   * Search customers
   */
  static async searchCustomers(query: string): Promise<Customer[]> {
    const response = await apiClient.get<Customer[]>(
      `${config.endpoints.customers.base}/search`,
      { params: { q: query } }
    );
    return response.data;
  }

  /**
   * Create a new customer
   */
  static async createCustomer(data: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
  }): Promise<Customer> {
    const response = await apiClient.post<Customer>(
      config.endpoints.customers.base,
      data
    );
    return response.data;
  }
}
