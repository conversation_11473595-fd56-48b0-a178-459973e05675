/**
 * Review and Rating related types
 */

import { Provider, User, Appointment } from './index';

export interface Review {
  id: number;
  rating: number; // 1-5 stars
  comment?: string;
  status: 'pending' | 'approved' | 'rejected';
  customerId: string;
  providerId: number;
  appointmentId?: number;
  serviceId?: number;
  createdAt: string;
  updatedAt: string;
  
  // Relations
  customer?: User;
  provider?: Provider;
  appointment?: Appointment;
  service?: Service;
  response?: ReviewResponse;
  moderationHistory?: ReviewModeration[];
}

export interface ReviewResponse {
  id: number;
  reviewId: number;
  response: string;
  respondedByUserId: string;
  createdAt: string;
  updatedAt: string;
  
  // Relations
  review?: Review;
  respondedBy?: User;
}

export interface ReviewModeration {
  id: number;
  reviewId: number;
  moderatedByUserId: string;
  previousStatus: 'pending' | 'approved' | 'rejected';
  newStatus: 'pending' | 'approved' | 'rejected';
  reason?: string;
  notes?: string;
  createdAt: string;
  
  // Relations
  review?: Review;
  moderatedBy?: User;
}

export interface ReviewCreateRequest {
  rating: number;
  comment?: string;
  appointmentId?: number;
  serviceId?: number;
}

export interface ReviewUpdateRequest {
  reviewId: number;
  rating?: number;
  comment?: string;
}

export interface ReviewResponseCreateRequest {
  reviewId: number;
  response: string;
}

export interface ReviewResponseUpdateRequest {
  responseId: number;
  response: string;
}

export interface ReviewModerationRequest {
  reviewId: number;
  status: 'approved' | 'rejected';
  reason?: string;
  notes?: string;
}

export interface ReviewFilters {
  rating?: number | number[];
  status?: 'pending' | 'approved' | 'rejected';
  customerId?: string;
  serviceId?: number;
  appointmentId?: number;
  hasResponse?: boolean;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
}

export interface ReviewStats {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  pendingReviews: number;
  approvedReviews: number;
  rejectedReviews: number;
  reviewsWithResponse: number;
  responseRate: number;
  recentReviews: Review[];
  topRatedServices: {
    serviceId: number;
    serviceName: string;
    averageRating: number;
    totalReviews: number;
  }[];
}

export interface RatingTrend {
  period: string; // "2024-01", "2024-02", etc.
  averageRating: number;
  totalReviews: number;
}

export interface ReviewSummary {
  overallRating: number;
  totalReviews: number;
  recentTrend: 'up' | 'down' | 'stable';
  strengths: string[];
  improvements: string[];
  commonKeywords: {
    positive: string[];
    negative: string[];
  };
}

// Forward declaration
export interface Service {
  id: number;
  title: string;
  // Other service fields...
}
