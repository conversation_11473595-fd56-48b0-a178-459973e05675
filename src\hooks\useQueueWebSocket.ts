import { useState, useEffect, useRef } from 'react';
import { QueueStateUpdate } from '../types/queue';

interface UseQueueWebSocketOptions {
  queueId?: number;
  enabled?: boolean;
  onQueueUpdate?: (update: QueueStateUpdate) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: Event) => void;
}

interface QueueWebSocketState {
  isConnected: boolean;
  queueState: QueueStateUpdate | null;
  error: string | null;
  reconnectAttempts: number;
}

/**
 * Hook for real-time queue updates via WebSocket
 * Based on the documentation's WebSocket integration patterns
 */
export const useQueueWebSocket = (options: UseQueueWebSocketOptions = {}) => {
  const {
    queueId,
    enabled = true,
    onQueueUpdate,
    onConnect,
    onDisconnect,
    onError
  } = options;

  const [state, setState] = useState<QueueWebSocketState>({
    isConnected: false,
    queueState: null,
    error: null,
    reconnectAttempts: 0,
  });

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const maxReconnectAttempts = 5;
  const reconnectDelay = 3000; // 3 seconds

  const connect = () => {
    if (!enabled) return;

    try {
      // Use secure WebSocket in production, regular WebSocket in development
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/ws`;
      
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('Queue WebSocket connected');
        setState(prev => ({
          ...prev,
          isConnected: true,
          error: null,
          reconnectAttempts: 0,
        }));

        // Join queue room if queueId is provided
        if (queueId) {
          wsRef.current?.send(JSON.stringify({
            type: 'join_queue',
            queueId: queueId
          }));
        }

        // Request initial queue status
        wsRef.current?.send(JSON.stringify({
          type: 'request_queue_status',
          queueId: queueId
        }));

        onConnect?.();
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          
          if (data.type === 'queue_update' && data.queueId === queueId) {
            const queueUpdate: QueueStateUpdate = {
              queueId: data.queueId,
              position: data.position,
              estimatedStartTime: data.estimatedStartTime,
              estimatedWaitMinutes: data.estimatedWaitMinutes,
              error: data.error,
            };

            setState(prev => ({
              ...prev,
              queueState: queueUpdate,
            }));

            onQueueUpdate?.(queueUpdate);
          } else if (data.type === 'provider_update') {
            // Handle provider-specific updates
            console.log('Provider queue update:', data);
          } else if (data.type === 'error') {
            setState(prev => ({
              ...prev,
              error: data.message,
            }));
          }
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log('Queue WebSocket disconnected:', event.code, event.reason);
        setState(prev => ({
          ...prev,
          isConnected: false,
        }));

        onDisconnect?.();

        // Attempt to reconnect if not manually closed
        if (event.code !== 1000 && state.reconnectAttempts < maxReconnectAttempts) {
          setState(prev => ({
            ...prev,
            reconnectAttempts: prev.reconnectAttempts + 1,
          }));

          reconnectTimeoutRef.current = setTimeout(() => {
            console.log(`Attempting to reconnect... (${state.reconnectAttempts + 1}/${maxReconnectAttempts})`);
            connect();
          }, reconnectDelay);
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('Queue WebSocket error:', error);
        setState(prev => ({
          ...prev,
          error: 'WebSocket connection error',
        }));
        onError?.(error);
      };

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      setState(prev => ({
        ...prev,
        error: 'Failed to establish WebSocket connection',
      }));
    }
  };

  const disconnect = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }
  };

  const sendMessage = (message: any) => {
    if (wsRef.current && state.isConnected) {
      wsRef.current.send(JSON.stringify(message));
    }
  };

  const notifyQueueChange = (queueId: number) => {
    sendMessage({
      type: 'notify_queue_change',
      queueId: queueId
    });
  };

  const requestQueueStatus = (queueId: number) => {
    sendMessage({
      type: 'request_queue_status',
      queueId: queueId
    });
  };

  // Connect on mount and when enabled/queueId changes
  useEffect(() => {
    if (enabled) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [enabled, queueId]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, []);

  return {
    ...state,
    connect,
    disconnect,
    sendMessage,
    notifyQueueChange,
    requestQueueStatus,
  };
};

/**
 * Hook for provider queue dashboard with real-time updates
 */
export const useProviderQueueUpdates = (queueIds: number[] = []) => {
  const [queueStates, setQueueStates] = useState<Record<number, QueueStateUpdate>>({});
  const [isConnected, setIsConnected] = useState(false);

  const { connect, disconnect, sendMessage } = useQueueWebSocket({
    enabled: queueIds.length > 0,
    onConnect: () => setIsConnected(true),
    onDisconnect: () => setIsConnected(false),
    onQueueUpdate: (update) => {
      setQueueStates(prev => ({
        ...prev,
        [update.queueId]: update,
      }));
    },
  });

  // Join all queue rooms when queueIds change
  useEffect(() => {
    if (isConnected && queueIds.length > 0) {
      queueIds.forEach(queueId => {
        sendMessage({
          type: 'join_queue',
          queueId: queueId
        });
      });
    }
  }, [isConnected, queueIds, sendMessage]);

  const refreshQueueStatus = (queueId: number) => {
    sendMessage({
      type: 'request_queue_status',
      queueId: queueId
    });
  };

  const refreshAllQueues = () => {
    queueIds.forEach(queueId => refreshQueueStatus(queueId));
  };

  return {
    queueStates,
    isConnected,
    refreshQueueStatus,
    refreshAllQueues,
    connect,
    disconnect,
  };
};
