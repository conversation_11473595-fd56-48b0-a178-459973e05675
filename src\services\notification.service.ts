import { apiClient } from '../lib/api-client';

export interface Notification {
  id: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  type: string; // e.g., 'APPOINTMENT_BOOKED_CUSTOMER'
  title: string;
  message: string;
  isRead: boolean;
  readAt: string | null;
  link: string;
  actorId: string;
  actor: {
    id: string;
    username: string | null;
    firstName: string;
    lastName: string;
  };
}

export class NotificationService {
  /**
   * Fetch notifications from the API
   */
  static async getNotifications(): Promise<{
    notifications: Notification[];
    unreadCount: number;
    totalCount: number;
  }> {
    const response = await apiClient.get<Notification[]>(
      '/api/auth/notifications/mobile/list'
    );

    const notifications = response.data;
    const unreadCount = notifications.filter(n => !n.isRead).length;

    return {
      notifications,
      unreadCount,
      totalCount: notifications.length
    };
  }

  /**
   * Mark a notification as read
   */
  static async markAsRead(notificationId: string): Promise<void> {
    await apiClient.post('/api/auth/notifications/mobile/mark-as-read', {
      notificationId: notificationId
    });
  }

  /**
   * Mark all notifications as read
   */
  static async markAllAsRead(): Promise<void> {
    await apiClient.post('/api/auth/notifications/mobile/mark-all-as-read');
  }

  /**
   * Delete a notification
   */
  static async deleteNotification(notificationId: string): Promise<void> {
    await apiClient.delete(`/api/auth/notifications/mobile/${notificationId}`);
  }

  /**
   * Get unread notification count
   */
  static async getUnreadCount(): Promise<number> {
    const notifications = await this.getNotifications();
    return notifications.unreadCount;
  }
}
