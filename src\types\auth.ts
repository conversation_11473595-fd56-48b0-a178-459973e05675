/**
 * Authentication related types
 */

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'CLIENT' | 'ADMIN';
  createdAt: string;
  updatedAt: string;
}

export interface LoginRequest {
  identifier: string; // Email or phone
  password: string;
}

export interface LoginResponse {
  message: string;
  user: User;
  provider: Provider;
  sessionId: string;
  token?: string;
  refreshToken?: string;
}

export interface OtpRequest {
  phoneNumber?: string;
  email?: string;
  firstName: string;
  lastName: string;
  password: string;
  isProviderRegistration: boolean;
  providerCategoryId: number;
  businessName: string;
  phone: string;
}

export interface VerifyOtpRegisterRequest {
  identifier: string; // Email or phone
  otp: string;
  password: string;
  firstName: string;
  lastName: string;
  providerCategoryId: number;
  businessName: string;
  phone: string;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetVerifyRequest {
  email: string;
  otp: string;
}

export interface PasswordResetVerifyResponse {
  resetToken: string;
  message: string;
}

export interface PasswordResetConfirmRequest {
  resetToken: string;
  newPassword: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  provider: Provider | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;
  subscription: UserSubscriptionData | null;
  subscriptionLoading: boolean;
  subscriptionError: string | null;
  usedCredits: UsedCreditsData | null;
  usedCreditsLoading: boolean;
  usedCreditsError: string | null;
  isTryingToLogIn: boolean;
}

// Subscription data integrated with auth
export interface UserSubscriptionData {
  subscription: {
    isActive: boolean;
    status: string;
    planId: string;
    planName: string;
    planDetails: {
      effect: {
        type: string;
        kind: string;
        amount: number;
        queues: number | null;
      };
      features: string[];
    };
  };
  user: {
    id: string;
    email: string;
    credits: number;
    queues: number;
    datePaid?: string;
  };
  hasCustomerPortal: boolean;
}

// Used credits data - Updated to match new API response
export interface UsedCreditsData {
  period: {
    type: string;
    startDate: string;
    endDate: string;
    month: number;
    year: number;
  };
  credits: {
    used: number;                    // Completed appointments in period
    remaining: number;               // Current credit balance
    allocated: number;               // Credits from subscription plan
    totalAvailable: number;          // Remaining + used
  };
  appointments: {
    completedMonth: number;          // Changes based on period (completedWeek, completedYear)
  };
  limits: {
    queues: number;
    usedQueues: number;
  };
  subscription: {
    planId: string;
    planName: string;
    status: string;
    isActive: boolean;
  };
}

// Used credits response
export interface UsedCreditsResponse {
  success: boolean;
  message: string;
  data: UsedCreditsData;
}

// Import Provider type (will be defined in provider.ts)
export interface Provider {
  id: number;
  userId: string;
  providerCategoryId: number;
  title: string;
  phone: string;
  presentation?: string;
  isVerified: boolean;
  isSetupComplete: boolean;
  logoId?: string;
  averageRating?: number;
  totalReviews: number;
  createdAt: string;
  updatedAt: string;
  category?: ProviderCategory;
  logo?: File;
}

export interface ProviderCategory {
  id: number;
  title: string;
  description?: string;
  isActive: boolean;
  sortOrder: number;
  metadata?: any;
  parentId?: number;
  imageId?: string;
  createdAt: string;
  updatedAt: string;
  parent?: ProviderCategory;
  children?: ProviderCategory[];
  image?: File;
}

export interface File {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  createdAt: string;
  updatedAt: string;
}
