/**
 * Hook for managing used credits data
 */

import { useEffect } from 'react';
import { useAuth } from '../context/AuthContext';

export const useUsedCredits = () => {
  const {
    usedCredits,
    usedCreditsLoading,
    usedCreditsError,
    fetchUsedCreditsData,
    refreshUsedCreditsData,
    getUsedCreditsThisMonth,
    getRemainingCredits,
    getMonthlyAllocatedCredits,
    getCompletedAppointmentsThisMonth,
    isAuthenticated,
  } = useAuth();

  // Auto-fetch data when authenticated
  useEffect(() => {
    if (isAuthenticated && !usedCredits && !usedCreditsLoading) {
      console.log('🔄 useUsedCredits: Auto-fetching credits data on mount');
      fetchUsedCreditsData();
    }
  }, [isAuthenticated, usedCredits, usedCreditsLoading, fetchUsedCreditsData]);



  // Derived values - Use totalAvailable for progress calculation
  const usagePercentage = usedCredits
    ? Math.round((1 - (usedCredits.credits.totalAvailable / usedCredits.credits.allocated)) * 100)
    : 0;

  const isNearLimit = usagePercentage >= 80;
  const isAtLimit = usagePercentage >= 95;

  const currentPeriod = usedCredits?.period ? {
    month: usedCredits.period.month,
    year: usedCredits.period.year,
    startDate: new Date(usedCredits.period.startDate),
    endDate: new Date(usedCredits.period.endDate),
  } : null;

  return {
    // Raw data
    usedCredits,
    isLoading: usedCreditsLoading,
    error: usedCreditsError,
    
    // Actions
    fetchData: fetchUsedCreditsData,
    refreshData: refreshUsedCreditsData,
    
    // Getters
    getUsedCreditsThisMonth,
    getRemainingCredits,
    getMonthlyAllocatedCredits,
    getCompletedAppointmentsThisMonth,
    
    // Derived values
    usagePercentage,
    isNearLimit,
    isAtLimit,
    currentPeriod,
    
    // Quick access to common values
    used: getUsedCreditsThisMonth(),
    remaining: getRemainingCredits(),
    allocated: getMonthlyAllocatedCredits(),
    totalAvailable: usedCredits?.credits?.totalAvailable || 0,
    completed: getCompletedAppointmentsThisMonth(),

    // Queue data from API
    totalQueues: usedCredits?.limits?.queues || 0,
    usedQueues: usedCredits?.limits?.usedQueues || 0,
    remainingQueues: (usedCredits?.limits?.queues || 0) - (usedCredits?.limits?.usedQueues || 0),
  };
};

export default useUsedCredits;
