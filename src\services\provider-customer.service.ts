import { apiClient, handleApiResponse, ApiResponse } from '../lib/api-client';
import { config } from '../lib/config';

/**
 * Provider Customer data structures based on documentation
 */
export interface ProviderCustomer {
  id: string;
  firstName: string;
  lastName: string;
  mobileNumber: string;
  email?: string;
  nationalId?: string;
  notes?: string;
  appointmentCount: number;
  createdAt: string;
  updatedAt?: string;
  isActive?: boolean;
}

export interface ProviderCustomerCreateRequest {
  firstName: string;
  lastName: string;
  mobileNumber: string;
  email?: string;
  nationalId?: string;
  notes?: string;
}

export interface ProviderCustomerUpdateRequest {
  customerUserId: string;
  firstName?: string;
  lastName?: string;
  mobileNumber?: string;
  email?: string;
  nationalId?: string;
  notes?: string;
}

export interface ProviderCustomerFilters {
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'email' | 'createdAt' | 'appointmentCount';
  sortOrder?: 'asc' | 'desc';
  isActive?: boolean;
}

export interface ProviderCustomerResponse {
  customers: ProviderCustomer[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface ProviderCustomerApiResponse {
  data: ProviderCustomer[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * Provider Customer Service
 * Handles all provider customer management operations
 */
export class ProviderCustomerService {
  /**
   * Get all provider customers with filtering and pagination
   */
  static async getProviderCustomers(filters?: ProviderCustomerFilters): Promise<ProviderCustomerResponse> {
    const response = await apiClient.get<ApiResponse<ProviderCustomerApiResponse>>(
      config.endpoints.customers.base,
      { params: filters }
    );
    const apiData = handleApiResponse(response);
    return {
      customers: apiData.data,
      pagination: apiData.pagination
    };
  }

  /**
   * Get a specific customer by ID
   */
  static async getProviderCustomer(customerId: string): Promise<ProviderCustomer> {
    const response = await apiClient.get<ApiResponse<ProviderCustomer>>(
      `${config.endpoints.customers.base}/${customerId}`
    );
    return handleApiResponse(response);
  }

  /**
   * Create a new customer
   */
  static async createProviderCustomer(data: ProviderCustomerCreateRequest): Promise<ProviderCustomer> {
    const response = await apiClient.post<ApiResponse<ProviderCustomer>>(
      config.endpoints.customers.base,
      data
    );
    return handleApiResponse(response);
  }

  /**
   * Update an existing customer
   */
  static async updateProviderCustomer(data: ProviderCustomerUpdateRequest): Promise<ProviderCustomer> {
    const response = await apiClient.put<ApiResponse<ProviderCustomer>>(
      `${config.endpoints.customers.base}/${data.customerUserId}`,
      data
    );
    return handleApiResponse(response);
  }

  /**
   * Search customers by query
   */
  static async searchProviderCustomers(query: string): Promise<ProviderCustomer[]> {
    const response = await apiClient.get<ApiResponse<ProviderCustomer[]>>(
      config.endpoints.customers.base,
      { params: { search: query } }
    );
    return handleApiResponse(response);
  }

  /**
   * Deactivate customer relationship (soft delete)
   */
  static async deactivateCustomerRelationship(customerId: string): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.patch<ApiResponse<{ success: boolean; message: string }>>(
      `${config.endpoints.customers.base}/${customerId}/deactivate`
    );
    return handleApiResponse(response);
  }
}
