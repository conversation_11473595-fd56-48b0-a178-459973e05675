import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Button from '../ui/button/Button';
import Label from '../form/Label';
import Input from '../form/input/InputField';
import { ErrorDisplay } from '../error';
import {
  useServiceCategories,
  useCreateServiceCategory,
  useUpdateServiceCategory,
  useDeleteServiceCategory,
} from '../../hooks/useServices';
import { ServiceCategory } from '../../types';

// Validation schema
const categorySchema = z.object({
  title: z.string().min(2, "Category name must be at least 2 characters"),
});

type CategoryFormData = z.infer<typeof categorySchema>;

interface ServiceCategoryManagerProps {
  onClose: () => void;
}

export default function ServiceCategoryManager({ onClose }: ServiceCategoryManagerProps) {
  const [editingCategory, setEditingCategory] = useState<ServiceCategory | null>(null);
  const [showForm, setShowForm] = useState(false);

  const { data: categories, isLoading } = useServiceCategories();
  const createCategoryMutation = useCreateServiceCategory();
  const updateCategoryMutation = useUpdateServiceCategory();
  const deleteCategoryMutation = useDeleteServiceCategory();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<CategoryFormData>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      title: '',
    },
  });

  const handleCreateCategory = () => {
    setEditingCategory(null);
    reset({
      title: '',
    });
    setShowForm(true);
  };

  const handleEditCategory = (category: ServiceCategory) => {
    setEditingCategory(category);
    reset({
      title: category.title,
    });
    setShowForm(true);
  };

  const handleDeleteCategory = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this category?')) {
      try {
        await deleteCategoryMutation.mutateAsync(id);
      } catch (error) {
        // Error handled by mutation
      }
    }
  };

  const onSubmit = async (data: CategoryFormData) => {
    try {
      if (editingCategory) {
        await updateCategoryMutation.mutateAsync({
          id: editingCategory.id,
          data,
        });
      } else {
        await createCategoryMutation.mutateAsync(data);
      }
      setShowForm(false);
      setEditingCategory(null);
    } catch (error) {
      // Error handled by mutations
    }
  };

  const isFormLoading = createCategoryMutation.isPending || updateCategoryMutation.isPending;
  const formError = createCategoryMutation.error || updateCategoryMutation.error;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-3xl overflow-hidden">
      <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Manage Service Categories
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            Create and organize your service categories
          </p>
        </div>
      </div>

      <div className="p-6">
            {!showForm ? (
              <div>
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    Categories
                  </h3>
                  <Button onClick={handleCreateCategory} size="sm">
                    Add Category
                  </Button>
                </div>

                {isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-500"></div>
                  </div>
                ) : categories && categories.length > 0 ? (
                  <div className="space-y-3">
                    {categories.map((category) => (
                      <div
                        key={category.id}
                        className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
                      >
                        <div className="flex-1">
                          <div className="flex items-center gap-3">
                            <div>
                              <h4 className="font-medium text-gray-900 dark:text-white">
                                {category.title}
                              </h4>
                              <p className="text-sm text-gray-500 dark:text-gray-400">
                                ID: {category.id}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            onClick={() => handleEditCategory(category)}
                            variant="outline"
                            size="sm"
                          >
                            Edit
                          </Button>
                          <Button
                            onClick={() => handleDeleteCategory(category.id)}
                            variant="outline"
                            size="sm"
                            className="text-red-600 border-red-300 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20"
                            disabled={deleteCategoryMutation.isPending}
                          >
                            Delete
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-gray-500 dark:text-gray-400 mb-4">
                      No categories found. Create your first category to organize your services.
                    </p>
                    <Button onClick={handleCreateCategory}>
                      Create First Category
                    </Button>
                  </div>
                )}
              </div>
            ) : (
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    {editingCategory ? 'Edit Category' : 'Create Category'}
                  </h3>
                  <Button
                    onClick={() => setShowForm(false)}
                    variant="outline"
                    size="sm"
                  >
                    Back to List
                  </Button>
                </div>

                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  <div>
                    <Label>
                      Category Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      {...register('title')}
                      placeholder="Enter category name"
                      disabled={isFormLoading}
                    />
                    {errors.title && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                        {errors.title.message}
                      </p>
                    )}
                  </div>

                  {formError && (
                    <ErrorDisplay
                      error={formError}
                      variant="banner"
                      size="sm"
                    />
                  )}

                  <div className="flex justify-end space-x-3">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setShowForm(false)}
                      disabled={isFormLoading}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={isFormLoading}
                    >
                      {isFormLoading 
                        ? (editingCategory ? 'Updating...' : 'Creating...') 
                        : (editingCategory ? 'Update Category' : 'Create Category')
                      }
                    </Button>
                  </div>
                </form>
              </div>
            )}
      </div>
    </div>
  );
}
