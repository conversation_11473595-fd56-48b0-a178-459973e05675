import { apiClient } from '../lib/api-client';
import { config } from '../lib/config';
import {
  Location,
  LocationCreateRequest,
  LocationUpdateRequest,
  Opening,
  LocationFilters,
  LocationStats,
} from '../types';

/**
 * Location management API service
 */
export class LocationService {
  /**
   * Get all locations for the provider
   */
  static async getLocations(filters?: LocationFilters): Promise<Location[]> {
    const response = await apiClient.get<{
      success: boolean;
      data: Location[];
      message: string;
    }>(
      config.endpoints.locations.base,
      { params: filters }
    );
    return response.data.data;
  }

  /**
   * Get location by ID
   */
  static async getLocation(id: number): Promise<Location> {
    const response = await apiClient.get<{
      success: boolean;
      data: Location;
      message: string;
    }>(`${config.endpoints.locations.base}/${id}`);
    return response.data.data;
  }

  /**
   * Create a new location
   */
  static async createLocation(data: LocationCreateRequest): Promise<Location> {
    const response = await apiClient.post<{
      success: boolean;
      data: Location;
      message: string;
    }>(
      config.endpoints.locations.base,
      data
    );
    return response.data.data;
  }

  /**
   * Update an existing location
   */
  static async updateLocation(id: number, data: LocationUpdateRequest): Promise<Location> {
    const response = await apiClient.put<{
      success: boolean;
      data: Location;
      message: string;
    }>(
      `${config.endpoints.locations.base}/${id}`,
      data
    );
    return response.data.data;
  }

  /**
   * Delete a location
   */
  static async deleteLocation(id: number): Promise<void> {
    const response = await apiClient.delete<{
      success: boolean;
      message: string;
    }>(`${config.endpoints.locations.base}/${id}`);

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to delete location');
    }
  }

  /**
   * Get location statistics
   */
  static async getLocationStats(): Promise<LocationStats> {
    const response = await apiClient.get<{
      success: boolean;
      data: LocationStats;
      message: string;
    }>(`${config.endpoints.locations.base}/stats`);
    return response.data.data;
  }

  /**
   * Get operating hours for a location
   */
  static async getLocationHours(locationId: number): Promise<Opening[]> {
    const response = await apiClient.get<{
      success: boolean;
      data: Opening[];
      message: string;
    }>(`${config.endpoints.locations.base}/${locationId}/hours`);
    return response.data.data;
  }

  /**
   * Update operating hours for a location
   */
  static async updateLocationHours(locationId: number, hours: Opening[]): Promise<Opening[]> {
    const response = await apiClient.put<{
      success: boolean;
      data: Opening[];
      message: string;
    }>(
      `${config.endpoints.locations.base}/${locationId}/hours`,
      { hours }
    );
    return response.data.data;
  }
}
