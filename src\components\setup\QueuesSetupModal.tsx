import React, { useState } from 'react';
import Button from '../ui/button/Button';
import { ErrorDisplay } from '../error';
import { useQueues, useDeleteQueue } from '../../hooks/useQueues';
import QueueCard from '../queues/QueueCard';
import QueueForm from '../queues/QueueForm';
import { Queue } from '../../types';

interface QueuesSetupModalProps {
  onComplete: () => void;
  onCancel: () => void;
}

export default function QueuesSetupModal({ onComplete, onCancel }: QueuesSetupModalProps) {
  const [showQueueForm, setShowQueueForm] = useState(false);
  const [editingQueue, setEditingQueue] = useState<Queue | null>(null);

  const { data: queues, isLoading, error } = useQueues();
  const deleteQueueMutation = useDeleteQueue();

  const handleCreateQueue = () => {
    setEditingQueue(null);
    setShowQueueForm(true);
  };

  const handleEditQueue = (queue: Queue) => {
    setEditingQueue(queue);
    setShowQueueForm(true);
  };

  const handleDeleteQueue = async (queue: Queue) => {
    if (window.confirm(`Are you sure you want to delete "${queue.title}"? This action cannot be undone.`)) {
      try {
        await deleteQueueMutation.mutateAsync(queue.id);
      } catch (error) {
        console.error('Failed to delete queue:', error);
      }
    }
  };

  const handleQueueFormClose = () => {
    setShowQueueForm(false);
    setEditingQueue(null);
  };

  const handleQueueFormSuccess = () => {
    handleQueueFormClose();
    // Don't auto-complete, let user decide when they're done
  };

  const handleComplete = () => {
    onComplete();
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-4"></div>
          <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Set Up Your Appointment Queues
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Create queues to manage customer appointments and wait times. Each queue can be assigned to specific services and locations.
        </p>
      </div>

      {/* Error Display */}
      {error && (
        <ErrorDisplay
          error={error}
          title="Failed to load queues"
          showRetry
          onRetry={() => window.location.reload()}
        />
      )}

      {/* Queue Form */}
      {showQueueForm && (
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6 bg-gray-50 dark:bg-gray-800/50">
          <QueueForm
            queue={editingQueue}
            onClose={handleQueueFormClose}
            onSuccess={handleQueueFormSuccess}
          />
        </div>
      )}

      {/* Existing Queues */}
      {!showQueueForm && (
        <>
          {queues && queues.length > 0 ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-md font-medium text-gray-900 dark:text-white">
                  Your Queues ({queues.length})
                </h4>
                <Button
                  onClick={handleCreateQueue}
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Add Another Queue
                </Button>
              </div>

              <div className="grid grid-cols-1 gap-4">
                {queues.map((queue) => (
                  <QueueCard
                    key={queue.id}
                    queue={queue}
                    onEdit={() => handleEditQueue(queue)}
                    onDelete={() => handleDeleteQueue(queue)}
                    onViewDetails={() => {}} // Not needed in setup
                    showActions={true}
                  />
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-12 bg-gray-50 dark:bg-gray-800/50 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600">
              <div className="mx-auto w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No Queues Yet
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Create your first appointment queue to start managing customer wait times.
              </p>
              <Button
                onClick={handleCreateQueue}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Create Your First Queue
              </Button>
            </div>
          )}
        </>
      )}

      {/* Action Buttons */}
      {!showQueueForm && (
        <div className="flex justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
          <Button
            variant="outline"
            onClick={onCancel}
          >
            Cancel
          </Button>
          
          <div className="flex gap-3">
            {queues && queues.length > 0 && (
              <Button
                onClick={handleComplete}
                className="bg-green-600 hover:bg-green-700"
              >
                Complete Setup
              </Button>
            )}
            <Button
              onClick={onCancel}
              variant="outline"
            >
              Skip for Now
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
