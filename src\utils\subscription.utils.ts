/**
 * Subscription utility functions for validation and status checks
 */

import { UserSubscriptionData, SubscriptionPlan } from '../types';

/**
 * Subscription validation results
 */
export interface SubscriptionValidationResult {
  isValid: boolean;
  reason?: string;
  upgradeRequired?: boolean;
  currentUsage?: {
    credits: number;
    queues: number;
  };
  limits?: {
    credits: number;
    queues: number;
  };
}

/**
 * Usage threshold configuration
 */
export interface UsageThresholds {
  creditWarning: number; // Percentage (0-1) when to show warning
  creditCritical: number; // Percentage (0-1) when to block actions
  queueWarning: number; // Percentage (0-1) when to show warning
  queueCritical: number; // Percentage (0-1) when to block actions
}

/**
 * Default usage thresholds
 */
export const DEFAULT_THRESHOLDS: UsageThresholds = {
  creditWarning: 0.2, // 20% remaining
  creditCritical: 0.05, // 5% remaining
  queueWarning: 0.8, // 80% used
  queueCritical: 0.95, // 95% used
};

/**
 * Check if user has an active subscription
 */
export const hasActiveSubscription = (subscription: UserSubscriptionData | null): boolean => {
  if (!subscription) return false;
  return subscription.subscription.isActive && subscription.subscription.status === 'active';
};

/**
 * Check if user is on a free plan
 */
export const isFreePlan = (subscription: UserSubscriptionData | null): boolean => {
  if (!subscription) return true;
  return subscription.subscription.planId === 'free';
};

/**
 * Get current credit usage percentage
 */
export const getCreditUsagePercentage = (subscription: UserSubscriptionData | null): number => {
  if (!subscription) return 0;
  
  const { credits } = subscription.user;
  const { amount } = subscription.subscription.planDetails.effect;
  
  if (amount === 0) return 0;
  return credits / amount;
};

/**
 * Get current queue usage percentage
 */
export const getQueueUsagePercentage = (subscription: UserSubscriptionData | null): number => {
  if (!subscription) return 0;
  
  const { queues } = subscription.user;
  const maxQueues = subscription.subscription.planDetails.effect.queues;
  
  if (!maxQueues || maxQueues === 0) return 0;
  return queues / maxQueues;
};

/**
 * Check if user can create a new queue
 */
export const canCreateQueue = (subscription: UserSubscriptionData | null): SubscriptionValidationResult => {
  if (!subscription) {
    return {
      isValid: false,
      reason: 'No subscription data available',
      upgradeRequired: true,
    };
  }

  const { queues } = subscription.user;
  const maxQueues = subscription.subscription.planDetails.effect.queues;

  if (!maxQueues) {
    return {
      isValid: false,
      reason: 'Queue creation not available on current plan',
      upgradeRequired: true,
    };
  }

  if (queues >= maxQueues) {
    return {
      isValid: false,
      reason: `Queue limit reached. You can create up to ${maxQueues} queue(s).`,
      upgradeRequired: true,
      currentUsage: { credits: subscription.user.credits, queues },
      limits: { credits: subscription.subscription.planDetails.effect.amount, queues: maxQueues },
    };
  }

  return {
    isValid: true,
    currentUsage: { credits: subscription.user.credits, queues },
    limits: { credits: subscription.subscription.planDetails.effect.amount, queues: maxQueues },
  };
};

/**
 * Check if user has sufficient credits for an action
 */
export const hasSufficientCredits = (
  subscription: UserSubscriptionData | null,
  requiredCredits: number
): SubscriptionValidationResult => {
  if (!subscription) {
    return {
      isValid: false,
      reason: 'No subscription data available',
      upgradeRequired: true,
    };
  }

  const { credits } = subscription.user;
  const maxCredits = subscription.subscription.planDetails.effect.amount;

  if (credits < requiredCredits) {
    return {
      isValid: false,
      reason: `Insufficient credits. Required: ${requiredCredits}, Available: ${credits}`,
      upgradeRequired: credits < maxCredits * DEFAULT_THRESHOLDS.creditCritical,
      currentUsage: { credits, queues: subscription.user.queues },
      limits: { credits: maxCredits, queues: subscription.subscription.planDetails.effect.queues || 0 },
    };
  }

  return {
    isValid: true,
    currentUsage: { credits, queues: subscription.user.queues },
    limits: { credits: maxCredits, queues: subscription.subscription.planDetails.effect.queues || 0 },
  };
};

/**
 * Check if user needs to upgrade based on usage
 */
export const needsUpgrade = (
  subscription: UserSubscriptionData | null,
  thresholds: UsageThresholds = DEFAULT_THRESHOLDS
): { credits: boolean; queues: boolean; overall: boolean } => {
  if (!subscription) {
    return { credits: true, queues: true, overall: true };
  }

  const creditUsage = getCreditUsagePercentage(subscription);
  const queueUsage = getQueueUsagePercentage(subscription);

  const creditsNeedUpgrade = creditUsage >= (1 - thresholds.creditCritical);
  const queuesNeedUpgrade = queueUsage >= thresholds.queueCritical;

  return {
    credits: creditsNeedUpgrade,
    queues: queuesNeedUpgrade,
    overall: creditsNeedUpgrade || queuesNeedUpgrade,
  };
};

/**
 * Get usage warnings
 */
export const getUsageWarnings = (
  subscription: UserSubscriptionData | null,
  thresholds: UsageThresholds = DEFAULT_THRESHOLDS
): Array<{ type: 'warning' | 'critical'; message: string; metric: 'credits' | 'queues' }> => {
  if (!subscription) return [];

  const warnings = [];
  const creditUsage = getCreditUsagePercentage(subscription);
  const queueUsage = getQueueUsagePercentage(subscription);

  // Credit warnings
  if (creditUsage >= (1 - thresholds.creditCritical)) {
    warnings.push({
      type: 'critical' as const,
      message: `Critical: Only ${subscription.user.credits} credits remaining`,
      metric: 'credits' as const,
    });
  } else if (creditUsage >= (1 - thresholds.creditWarning)) {
    warnings.push({
      type: 'warning' as const,
      message: `Warning: ${subscription.user.credits} credits remaining`,
      metric: 'credits' as const,
    });
  }

  // Queue warnings
  if (queueUsage >= thresholds.queueCritical) {
    warnings.push({
      type: 'critical' as const,
      message: `Critical: ${subscription.user.queues} of ${subscription.subscription.planDetails.effect.queues} queues used`,
      metric: 'queues' as const,
    });
  } else if (queueUsage >= thresholds.queueWarning) {
    warnings.push({
      type: 'warning' as const,
      message: `Warning: ${subscription.user.queues} of ${subscription.subscription.planDetails.effect.queues} queues used`,
      metric: 'queues' as const,
    });
  }

  return warnings;
};

/**
 * Get recommended plan based on usage patterns
 */
export const getRecommendedPlan = (
  subscription: UserSubscriptionData | null,
  plans: SubscriptionPlan[]
): SubscriptionPlan | null => {
  if (!subscription || !plans.length) return null;

  const currentPlan = plans.find(p => p.id === subscription.subscription.planId);
  if (!currentPlan) return null;

  // If user is frequently hitting limits, recommend upgrade
  const warnings = getUsageWarnings(subscription);
  const hasCriticalWarnings = warnings.some(w => w.type === 'critical');

  if (hasCriticalWarnings) {
    // Find next tier up
    const currentCredits = currentPlan.effect.amount;
    const betterPlans = plans
      .filter(p => p.effect.amount > currentCredits && p.isSubscription)
      .sort((a, b) => a.effect.amount - b.effect.amount);

    return betterPlans[0] || null;
  }

  return null;
};

/**
 * Format usage display text
 */
export const formatUsageText = (
  current: number,
  limit: number,
  type: 'credits' | 'queues'
): string => {
  const percentage = limit > 0 ? Math.round((current / limit) * 100) : 0;
  const unit = type === 'credits' ? 'credits' : 'queues';
  
  return `${current}/${limit} ${unit} (${percentage}%)`;
};

/**
 * Get plan comparison data
 */
export const comparePlans = (
  currentPlan: SubscriptionPlan,
  targetPlan: SubscriptionPlan
): {
  isUpgrade: boolean;
  isDowngrade: boolean;
  creditsDifference: number;
  queuesDifference: number;
  features: { added: string[]; removed: string[] };
} => {
  const isUpgrade = targetPlan.effect.amount > currentPlan.effect.amount;
  const isDowngrade = targetPlan.effect.amount < currentPlan.effect.amount;
  
  const creditsDifference = targetPlan.effect.amount - currentPlan.effect.amount;
  const queuesDifference = (targetPlan.effect.queues || 0) - (currentPlan.effect.queues || 0);

  // Simple feature comparison (in a real app, this would be more sophisticated)
  const currentFeatures = new Set(currentPlan.features);
  const targetFeatures = new Set(targetPlan.features);
  
  const added = targetPlan.features.filter(f => !currentFeatures.has(f));
  const removed = currentPlan.features.filter(f => !targetFeatures.has(f));

  return {
    isUpgrade,
    isDowngrade,
    creditsDifference,
    queuesDifference,
    features: { added, removed },
  };
};
