/**
 * Profile Completion Demo Page
 * Demonstrates the profile completion feature with different scenarios
 */

import React, { useState } from 'react';
import PageMeta from '../components/common/PageMeta';
import { ProfileCompletionCard } from '../components/profile-completion';
import { calculateProfileCompletion } from '../utils/profile-completion';
import { ProfileCompletionData } from '../types/profile-completion';
import { runProfileCompletionTests, testSpecificScenarios } from '../utils/test-profile-completion';

const ProfileCompletionDemo: React.FC = () => {
  const [selectedScenario, setSelectedScenario] = useState('empty');
  const [testResults, setTestResults] = useState<string>('');

  // Mock user
  const mockUser = {
    id: 'demo-user',
    email: '<EMAIL>',
    firstName: 'Demo',
    lastName: 'User',
    role: 'CLIENT' as const,
    isEmailVerified: true,
    isPhoneVerified: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  };

  // Demo scenarios
  const scenarios = {
    empty: {
      name: 'Empty Provider (0%)',
      provider: {
        id: 1,
        userId: 'demo-user',
        isSetupComplete: false,
        isVerified: false,
        averageRating: 0,
        totalReviews: 0,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        providingPlaces: [],
        services: [],
        queues: [],
      },
    },
    partial: {
      name: 'Partial Setup (65%)',
      provider: {
        id: 1,
        userId: 'demo-user',
        title: 'Demo Business',
        phone: '+**********',
        presentation: 'This is a demo business for testing profile completion.',
        providerCategoryId: 1,
        isSetupComplete: false,
        isVerified: false,
        averageRating: 0,
        totalReviews: 0,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        category: {
          id: 1,
          title: 'Demo Category',
          description: 'Demo category',
          isActive: true,
          sortOrder: 0,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
        providingPlaces: [
          {
            id: 1,
            sProviderId: 1,
            name: 'Main Office',
            address: '123 Demo Street',
            city: 'Demo City',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
          },
        ],
        services: [
          {
            id: 1,
            sProviderId: 1,
            title: 'Demo Service',
            duration: 60,
            price: 100,
            pointsRequirements: 1,
            isPublic: true,
            deliveryType: 'at_location' as const,
            servedRegions: ['Demo City'],
            color: '#3B82F6',
            acceptOnline: true,
            acceptNew: true,
            notificationOn: true,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
          },
        ],
        queues: [],
      },
    },
    complete: {
      name: 'Complete Setup (100%)',
      provider: {
        id: 1,
        userId: 'demo-user',
        title: 'Complete Demo Business',
        phone: '+**********',
        presentation: 'This is a complete demo business with all sections filled.',
        providerCategoryId: 1,
        logoId: 'demo-logo',
        isSetupComplete: false,
        isVerified: false,
        averageRating: 4.8,
        totalReviews: 25,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        category: {
          id: 1,
          title: 'Demo Category',
          description: 'Demo category',
          isActive: true,
          sortOrder: 0,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
        logo: {
          id: 'demo-logo',
          filename: 'logo.png',
          originalName: 'business-logo.png',
          mimeType: 'image/png',
          size: 2048,
          url: 'https://via.placeholder.com/200x200/3B82F6/FFFFFF?text=LOGO',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
        providingPlaces: [
          {
            id: 1,
            sProviderId: 1,
            name: 'Main Office',
            address: '123 Demo Street',
            city: 'Demo City',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
          },
          {
            id: 2,
            sProviderId: 1,
            name: 'Branch Office',
            address: '456 Demo Avenue',
            city: 'Demo City',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
          },
        ],
        services: [
          {
            id: 1,
            sProviderId: 1,
            title: 'Premium Service',
            duration: 60,
            price: 150,
            pointsRequirements: 1,
            isPublic: true,
            deliveryType: 'at_location' as const,
            servedRegions: ['Demo City'],
            color: '#3B82F6',
            acceptOnline: true,
            acceptNew: true,
            notificationOn: true,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
          },
          {
            id: 2,
            sProviderId: 1,
            title: 'Standard Service',
            duration: 30,
            price: 75,
            pointsRequirements: 1,
            isPublic: true,
            deliveryType: 'both' as const,
            servedRegions: ['Demo City'],
            color: '#10B981',
            acceptOnline: true,
            acceptNew: true,
            notificationOn: true,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
          },
        ],
        queues: [
          {
            id: 1,
            sProviderId: 1,
            sProvidingPlaceId: 1,
            title: 'Main Queue',
            isActive: true,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
          },
          {
            id: 2,
            sProviderId: 1,
            sProvidingPlaceId: 2,
            title: 'Branch Queue',
            isActive: true,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
          },
        ],
      },
    },
  };

  const currentScenario = scenarios[selectedScenario as keyof typeof scenarios];
  const completionData: ProfileCompletionData = {
    user: mockUser,
    provider: currentScenario.provider,
  };

  const completion = calculateProfileCompletion(completionData);

  const handleRunTests = () => {
    console.log('Running profile completion tests...');
    
    // Capture console output
    const originalLog = console.log;
    let output = '';
    console.log = (...args) => {
      output += args.join(' ') + '\n';
      originalLog(...args);
    };

    try {
      runProfileCompletionTests();
      testSpecificScenarios();
      setTestResults(output);
    } catch (error) {
      setTestResults(`Error running tests: ${error}`);
    } finally {
      console.log = originalLog;
    }
  };

  const handleActionClick = (section: string, action: string) => {
    alert(`Navigation: ${section} -> ${action}`);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <PageMeta
        title="Profile Completion Demo | Test the Feature"
        description="Interactive demo of the profile completion feature with different scenarios"
      />

      <div className="container mx-auto px-4 py-8 space-y-6">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Profile Completion Feature Demo
          </h1>
          <p className="text-gray-600">
            Test the profile completion feature with different provider scenarios.
            This demo shows how the completion percentage is calculated and how the UI responds.
          </p>
        </div>

        {/* Scenario Selector */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Select Scenario</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {Object.entries(scenarios).map(([key, scenario]) => (
              <button
                key={key}
                onClick={() => setSelectedScenario(key)}
                className={`p-4 rounded-lg border-2 transition-colors ${
                  selectedScenario === key
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:border-gray-300 text-gray-700'
                }`}
              >
                <div className="font-medium">{scenario.name}</div>
              </button>
            ))}
          </div>
        </div>

        {/* Profile Completion Card */}
        <ProfileCompletionCard
          data={completionData}
          showDetails={true}
          onActionClick={handleActionClick}
          className="shadow-lg"
        />

        {/* Completion Details */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Completion Analysis</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Overall Status</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Percentage:</span>
                  <span className="font-medium">{completion.overallPercentage}%</span>
                </div>
                <div className="flex justify-between">
                  <span>Completed:</span>
                  <span className={completion.overallCompleted ? 'text-green-600' : 'text-red-600'}>
                    {completion.overallCompleted ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Should Mark Complete:</span>
                  <span className={completion.shouldMarkAsComplete ? 'text-green-600' : 'text-gray-600'}>
                    {completion.shouldMarkAsComplete ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Section Breakdown</h3>
              <div className="space-y-1 text-sm">
                {Object.entries(completion.breakdown).map(([section, data]) => (
                  <div key={section} className="flex justify-between">
                    <span className="capitalize">{section.replace(/([A-Z])/g, ' $1').trim()}:</span>
                    <span className={`font-medium ${data.completed ? 'text-green-600' : 'text-red-600'}`}>
                      {data.percentage}%
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Test Runner */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Test Runner</h2>
          <button
            onClick={handleRunTests}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors mb-4"
          >
            Run Profile Completion Tests
          </button>
          
          {testResults && (
            <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
              <h3 className="font-medium text-gray-900 mb-2">Test Results:</h3>
              <pre className="text-xs text-gray-700 whitespace-pre-wrap overflow-x-auto">
                {testResults}
              </pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfileCompletionDemo;
