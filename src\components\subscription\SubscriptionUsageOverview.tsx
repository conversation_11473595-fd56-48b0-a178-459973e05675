import React from 'react';
import { useUsedCredits } from '../../hooks/useUsedCredits';
import { useSubscriptionStatus } from '../../hooks/useSubscription';
import { ErrorDisplay } from '../error';
import Button from '../ui/button/Button';

interface SubscriptionUsageOverviewProps {
  className?: string;
}

export default function SubscriptionUsageOverview({ className = '' }: SubscriptionUsageOverviewProps) {
  const {
    used,
    remaining,
    allocated,
    totalAvailable,
    completed,
    usagePercentage,
    isNearLimit,
    isAtLimit,
    currentPeriod,
    isLoading: creditsLoading,
    error: creditsError,
    refreshData: refreshCreditsData
  } = useUsedCredits();

  const { data: statusData, isLoading: statusLoading } = useSubscriptionStatus();
  console.log('statusData', statusData);
  const isLoading = creditsLoading || statusLoading;
  const currentPlan = statusData?.data?.subscription?.planId || 'free';
  const queueLimit = statusData?.data?.user?.queues || 1;

  // Debug logging
  React.useEffect(() => {
    console.log('📊 SubscriptionUsageOverview - Raw hook data:', {
      creditsLoading,
      creditsError,
      used,
      remaining,
      allocated,
      totalAvailable,
      completed,
      usagePercentage,
      isNearLimit,
      isAtLimit,
      currentPeriod
    });
    console.log('📊 SubscriptionUsageOverview - Subscription Data:', statusData);
  }, [creditsLoading, used, remaining, allocated, totalAvailable, statusData]);
  
  // Get queue data - for now using mock data, but queue limit comes from API
  const activeQueues = 1; // TODO: This should come from actual queue count API
  const queueUsagePercentage = Math.round((activeQueues / queueLimit) * 100);

  if (isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="space-y-4">
            <div className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (creditsError) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700 ${className}`}>
        <ErrorDisplay
          error={creditsError}
          title="Failed to load usage data"
          showRetry
          onRetry={refreshCreditsData}
        />
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Usage Statistics
        </h3>
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {currentPeriod ? `${currentPeriod.month}/${currentPeriod.year}` : 'This Month'}
        </span>
      </div>

      <div className="space-y-6">
        {/* Credits Usage */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
              </svg>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Credits Used</span>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {used}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                of {totalAvailable}
              </div>
            </div>
          </div>
          
          {/* Credits Progress Bar */}
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                isAtLimit ? 'bg-red-500' : 
                isNearLimit ? 'bg-yellow-500' : 
                'bg-green-500'
              }`}
              style={{ width: `${Math.min(usagePercentage, 100)}%` }}
            ></div>
          </div>
          
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>{usagePercentage.toFixed(1)}% used</span>
            <span>{remaining} remaining</span>
          </div>
        </div>

        {/* Queues Usage */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-purple-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Queues Active</span>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {activeQueues}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                of {queueLimit}
              </div>
            </div>
          </div>
          
          {/* Queues Progress Bar */}
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                queueUsagePercentage >= 90 ? 'bg-red-500' : 
                queueUsagePercentage >= 70 ? 'bg-yellow-500' : 
                'bg-green-500'
              }`}
              style={{ width: `${Math.min(queueUsagePercentage, 100)}%` }}
            ></div>
          </div>
          
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>{queueUsagePercentage}% used</span>
            <span>{queueLimit - activeQueues} available</span>
          </div>
        </div>

        {/* Usage Warning */}
        {(isAtLimit || isNearLimit) && (
          <div className={`p-4 rounded-lg border ${
            isAtLimit 
              ? 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800' 
              : 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'
          }`}>
            <div className="flex items-center">
              <svg className={`w-5 h-5 mr-2 ${
                isAtLimit ? 'text-red-500' : 'text-yellow-500'
              }`} fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <span className={`text-sm font-medium ${
                isAtLimit 
                  ? 'text-red-800 dark:text-red-200' 
                  : 'text-yellow-800 dark:text-yellow-200'
              }`}>
                {isAtLimit 
                  ? "You're at your usage limits. Consider upgrading your plan." 
                  : "You're approaching your plan limits. Consider upgrading."
                }
              </span>
            </div>
          </div>
        )}

        {/* Current Plan Info */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
          <span className="text-sm text-gray-600 dark:text-gray-400">Current Plan:</span>
          <span className="text-sm font-medium text-gray-900 dark:text-white capitalize">
            {currentPlan}
          </span>
        </div>
      </div>
    </div>
  );
}
