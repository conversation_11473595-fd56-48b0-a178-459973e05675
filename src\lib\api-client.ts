import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { config } from './config';

/**
 * API Response wrapper type
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message: string;
  errors?: any[];
}

/**
 * API Error type
 */
export interface ApiError {
  message: string;
  status: number;
  errors?: any[];
}

/**
 * Authentication token management
 */
class TokenManager {
  static getToken(): string | null {
    return localStorage.getItem(config.auth.tokenKey);
  }

  static setToken(token: string): void {
    localStorage.setItem(config.auth.tokenKey, token);
  }

  static removeToken(): void {
    localStorage.removeItem(config.auth.tokenKey);
    localStorage.removeItem(config.auth.refreshTokenKey);
    localStorage.removeItem(config.auth.userKey);
  }

  static getRefreshToken(): string | null {
    return localStorage.getItem(config.auth.refreshTokenKey);
  }

  static setRefreshToken(token: string): void {
    localStorage.setItem(config.auth.refreshTokenKey, token);
  }
}

/**
 * Create axios instance with default configuration
 */
const createApiClient = (): AxiosInstance => {
  const client = axios.create({
    baseURL: config.api.baseUrl,
    timeout: config.api.timeout,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor to add auth token
  client.interceptors.request.use(
    (config) => {
      const token = TokenManager.getToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
        // Update last activity timestamp on authenticated requests
        localStorage.setItem('provider_last_activity', Date.now().toString());
      }

      // Debug logging
      if (import.meta.env.VITE_DEBUG_API === 'true') {
        console.log('🚀 API Request:', {
          method: config.method?.toUpperCase(),
          url: config.url,
          data: config.data,
          headers: config.headers,
        });
      }

      return config;
    },
    (error) => {
      console.error('❌ Request Error:', error);
      return Promise.reject(error);
    }
  );

  // Response interceptor for error handling and token refresh
  client.interceptors.response.use(
    (response: AxiosResponse) => {
      // Debug logging
      if (config.debug.api) {
        console.log('✅ API Response:', {
          status: response.status,
          url: response.config.url,
          data: response.data,
        });
      }

      return response;
    },
    async (error: AxiosError) => {
      const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

      // Debug logging
      if (config.debug.api) {
        console.error('❌ API Error:', {
          status: error.response?.status,
          url: error.config?.url,
          message: error.message,
          data: error.response?.data,
        });
      }

      // Handle 401 errors (unauthorized)
      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;

        console.warn('🔐 401 Unauthorized - Attempting token refresh for:', originalRequest.url);

        try {
          const refreshToken = TokenManager.getRefreshToken();
          if (refreshToken) {
            console.log('🔄 Attempting to refresh token...');
            // Attempt to refresh token
            const response = await axios.post(
              `${config.api.baseUrl}${config.endpoints.auth.refreshToken}`,
              { refreshToken }
            );

            const { token } = response.data;
            TokenManager.setToken(token);
            console.log('✅ Token refreshed successfully');

            // Retry original request with new token
            if (originalRequest.headers) {
              originalRequest.headers.Authorization = `Bearer ${token}`;
            }

            return client(originalRequest);
          } else {
            console.warn('❌ No refresh token available');
            TokenManager.removeToken();
            window.location.href = '/signin';
            return Promise.reject(new Error('No refresh token available'));
          }
        } catch (refreshError) {
          // Refresh failed, redirect to login
          console.error('❌ Token refresh failed:', refreshError);
          TokenManager.removeToken();
          window.location.href = '/signin';
          return Promise.reject(refreshError);
        }
      }

      // Handle other errors
      const apiError: ApiError = {
        message: error.response?.data?.message || error.message || 'An unexpected error occurred',
        status: error.response?.status || 500,
        errors: error.response?.data?.errors,
      };

      return Promise.reject(apiError);
    }
  );

  return client;
};

// Create and export the API client instance
export const apiClient = createApiClient();

// Export token manager for use in auth context
export { TokenManager };

// Utility function to handle API responses
export const handleApiResponse = <T>(response: AxiosResponse<ApiResponse<T>>): T => {
  if (response.data.success) {
    return response.data.data;
  }
  throw new Error(response.data.message || 'API request failed');
};

// Utility function to create API error from axios error
export const createApiError = (error: any): ApiError => {
  if (error.response) {
    return {
      message: error.response.data?.message || 'Server error',
      status: error.response.status,
      errors: error.response.data?.errors,
    };
  }
  
  if (error.request) {
    return {
      message: 'Network error - please check your connection',
      status: 0,
    };
  }
  
  return {
    message: error.message || 'An unexpected error occurred',
    status: 500,
  };
};
