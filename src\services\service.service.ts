import { apiClient } from '../lib/api-client';
import { config } from '../lib/config';
import {
  Service,
  ServiceCreateRequest,
  ServiceUpdateRequest,
  ServiceCategory,
  ServiceCategoryCreateRequest,
  ServiceCategoryUpdateRequest,
  ServiceFilters,
  ServiceStats,
} from '../types';

/**
 * Service management API service
 */
export class ServiceService {
  /**
   * Get all services for the provider
   */
  static async getServices(filters?: ServiceFilters): Promise<Service[]> {
    const response = await apiClient.get<{
      success: boolean;
      data: Service[];
      message: string;
    }>(
      config.endpoints.services.base,
      { params: filters }
    );
    return response.data.data;
  }

  /**
   * Get service by ID
   */
  static async getService(id: number): Promise<Service> {
    const response = await apiClient.get<{
      success: boolean;
      data: Service;
      message: string;
    }>(`${config.endpoints.services.base}/${id}`);
    return response.data.data;
  }

  /**
   * Create a new service
   */
  static async createService(data: ServiceCreateRequest): Promise<Service> {
    const response = await apiClient.post<{
      success: boolean;
      data: Service;
      message: string;
    }>(
      config.endpoints.services.base,
      data
    );
    return response.data.data;
  }

  /**
   * Update an existing service
   */
  static async updateService(id: number, data: ServiceUpdateRequest): Promise<Service> {
    const response = await apiClient.put<{
      success: boolean;
      data: Service;
      message: string;
    }>(
      `${config.endpoints.services.base}/${id}`,
      data
    );
    return response.data.data;
  }

  /**
   * Delete a service
   */
  static async deleteService(id: number): Promise<void> {
    const response = await apiClient.delete<{
      success: boolean;
      message: string;
    }>(`${config.endpoints.services.base}/${id}`);

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to delete service');
    }
  }

  /**
   * Get service statistics
   */
  static async getServiceStats(): Promise<ServiceStats> {
    const response = await apiClient.get<{
      success: boolean;
      data: ServiceStats;
      message: string;
    }>(`${config.endpoints.services.base}/stats`);
    return response.data.data;
  }

  // Service Categories
  /**
   * Get all service categories for the provider
   */
  static async getServiceCategories(): Promise<ServiceCategory[]> {
    const response = await apiClient.get<{
      success: boolean;
      data: ServiceCategory[];
      message: string;
    }>(
      config.endpoints.services.categories
    );
    return response.data.data;
  }

  /**
   * Create a new service category
   */
  static async createServiceCategory(data: ServiceCategoryCreateRequest): Promise<ServiceCategory> {
    const response = await apiClient.post<{
      success: boolean;
      data: ServiceCategory;
      message: string;
    }>(
      config.endpoints.services.categories,
      data
    );
    return response.data.data;
  }

  /**
   * Update a service category
   */
  static async updateServiceCategory(id: number, data: ServiceCategoryUpdateRequest): Promise<ServiceCategory> {
    const response = await apiClient.put<{
      success: boolean;
      data: ServiceCategory;
      message: string;
    }>(
      `${config.endpoints.services.categories}/${id}`,
      data
    );
    return response.data.data;
  }

  /**
   * Delete a service category
   */
  static async deleteServiceCategory(id: number): Promise<void> {
    const response = await apiClient.delete<{
      success: boolean;
      message: string;
    }>(`${config.endpoints.services.categories}/${id}`);

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to delete category');
    }
  }
}
