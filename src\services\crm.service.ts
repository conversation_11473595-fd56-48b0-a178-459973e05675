import { apiClient } from '../lib/api-client';
import { config } from '../lib/config';
import {
  Customer,
  CustomerFolder,
  CustomerCommunication,
  CustomerCreateRequest,
  CustomerUpdateRequest,
  CustomerFolderUpdateRequest,
  CustomerCommunicationCreateRequest,
  CustomerFilters,
  CustomerStats,
  CustomerHistory,
  CustomerSegment,
  CustomerInsights,
  CustomerRecommendation,
} from '../types/customer';

/**
 * Enhanced Customer Relationship Management Service
 * Handles all CRM-related API operations
 */
export class CRMService {
  /**
   * Get all customers with advanced filtering
   */
  static async getCustomers(filters?: CustomerFilters): Promise<Customer[]> {
    const response = await apiClient.get<{
      success: boolean;
      data: Customer[];
      message: string;
    }>(
      config.endpoints.customers?.base || '/customers',
      { params: filters }
    );
    return response.data.data;
  }

  /**
   * Get a specific customer with full details
   */
  static async getCustomer(id: string): Promise<Customer> {
    const response = await apiClient.get<{
      success: boolean;
      data: Customer;
      message: string;
    }>(`${config.endpoints.customers?.base || '/customers'}/${id}`);
    return response.data.data;
  }

  /**
   * Get customer folder (provider-specific data)
   */
  static async getCustomerFolder(customerId: string): Promise<CustomerFolder> {
    const response = await apiClient.get<{
      success: boolean;
      data: CustomerFolder;
      message: string;
    }>(`${config.endpoints.customers?.base || '/customers'}/${customerId}/folder`);
    return response.data.data;
  }

  /**
   * Create a new customer
   */
  static async createCustomer(data: CustomerCreateRequest): Promise<Customer> {
    const response = await apiClient.post<{
      success: boolean;
      data: Customer;
      message: string;
    }>(
      config.endpoints.customers?.base || '/customers',
      data
    );
    return response.data.data;
  }

  /**
   * Update customer information
   */
  static async updateCustomer(data: CustomerUpdateRequest): Promise<Customer> {
    const response = await apiClient.put<{
      success: boolean;
      data: Customer;
      message: string;
    }>(
      `${config.endpoints.customers?.base || '/customers'}/${data.customerId}`,
      data
    );
    return response.data.data;
  }

  /**
   * Update customer folder (provider-specific data)
   */
  static async updateCustomerFolder(data: CustomerFolderUpdateRequest): Promise<CustomerFolder> {
    const response = await apiClient.put<{
      success: boolean;
      data: CustomerFolder;
      message: string;
    }>(
      `${config.endpoints.customers?.base || '/customers'}/${data.customerId}/folder`,
      data
    );
    return response.data.data;
  }

  /**
   * Get customer statistics
   */
  static async getCustomerStats(): Promise<CustomerStats> {
    const response = await apiClient.get<{
      success: boolean;
      data: CustomerStats;
      message: string;
    }>(`${config.endpoints.customers?.base || '/customers'}/stats`);
    return response.data.data;
  }

  /**
   * Get customer history and analytics
   */
  static async getCustomerHistory(customerId: string): Promise<CustomerHistory> {
    const response = await apiClient.get<{
      success: boolean;
      data: CustomerHistory;
      message: string;
    }>(`${config.endpoints.customers?.base || '/customers'}/${customerId}/history`);
    return response.data.data;
  }

  /**
   * Get customer communications
   */
  static async getCustomerCommunications(customerId: string): Promise<CustomerCommunication[]> {
    const response = await apiClient.get<{
      success: boolean;
      data: CustomerCommunication[];
      message: string;
    }>(`${config.endpoints.customers?.base || '/customers'}/${customerId}/communications`);
    return response.data.data;
  }

  /**
   * Add customer communication
   */
  static async addCustomerCommunication(data: CustomerCommunicationCreateRequest): Promise<CustomerCommunication> {
    const response = await apiClient.post<{
      success: boolean;
      data: CustomerCommunication;
      message: string;
    }>(
      `${config.endpoints.customers?.base || '/customers'}/${data.customerId}/communications`,
      data
    );
    return response.data.data;
  }

  /**
   * Get customer segments
   */
  static async getCustomerSegments(): Promise<CustomerSegment[]> {
    const response = await apiClient.get<{
      success: boolean;
      data: CustomerSegment[];
      message: string;
    }>(`${config.endpoints.customers?.base || '/customers'}/segments`);
    return response.data.data;
  }

  /**
   * Create customer segment
   */
  static async createCustomerSegment(data: Omit<CustomerSegment, 'id' | 'customerCount' | 'createdAt' | 'updatedAt'>): Promise<CustomerSegment> {
    const response = await apiClient.post<{
      success: boolean;
      data: CustomerSegment;
      message: string;
    }>(
      `${config.endpoints.customers?.base || '/customers'}/segments`,
      data
    );
    return response.data.data;
  }

  /**
   * Get customers in a specific segment
   */
  static async getCustomersInSegment(segmentId: number): Promise<Customer[]> {
    const response = await apiClient.get<{
      success: boolean;
      data: Customer[];
      message: string;
    }>(`${config.endpoints.customers?.base || '/customers'}/segments/${segmentId}/customers`);
    return response.data.data;
  }

  /**
   * Get customer insights and recommendations
   */
  static async getCustomerInsights(customerId: string): Promise<CustomerInsights> {
    const response = await apiClient.get<{
      success: boolean;
      data: CustomerInsights;
      message: string;
    }>(`${config.endpoints.customers?.base || '/customers'}/${customerId}/insights`);
    return response.data.data;
  }

  /**
   * Get customer recommendations
   */
  static async getCustomerRecommendations(customerId: string): Promise<CustomerRecommendation[]> {
    const response = await apiClient.get<{
      success: boolean;
      data: CustomerRecommendation[];
      message: string;
    }>(`${config.endpoints.customers?.base || '/customers'}/${customerId}/recommendations`);
    return response.data.data;
  }

  /**
   * Search customers with advanced criteria
   */
  static async searchCustomers(query: string, filters?: CustomerFilters): Promise<Customer[]> {
    const response = await apiClient.get<{
      success: boolean;
      data: Customer[];
      message: string;
    }>(
      `${config.endpoints.customers?.base || '/customers'}/search`,
      { params: { q: query, ...filters } }
    );
    return response.data.data;
  }

  /**
   * Get customer loyalty points history
   */
  static async getCustomerLoyaltyHistory(customerId: string): Promise<any[]> {
    const response = await apiClient.get<{
      success: boolean;
      data: any[];
      message: string;
    }>(`${config.endpoints.customers?.base || '/customers'}/${customerId}/loyalty`);
    return response.data.data;
  }

  /**
   * Add loyalty points to customer
   */
  static async addLoyaltyPoints(customerId: string, points: number, reason: string): Promise<void> {
    await apiClient.post(
      `${config.endpoints.customers?.base || '/customers'}/${customerId}/loyalty`,
      { points, reason }
    );
  }

  /**
   * Get customer tags
   */
  static async getCustomerTags(): Promise<string[]> {
    const response = await apiClient.get<{
      success: boolean;
      data: string[];
      message: string;
    }>(`${config.endpoints.customers?.base || '/customers'}/tags`);
    return response.data.data;
  }

  /**
   * Add tag to customer
   */
  static async addCustomerTag(customerId: string, tag: string): Promise<void> {
    await apiClient.post(
      `${config.endpoints.customers?.base || '/customers'}/${customerId}/tags`,
      { tag }
    );
  }

  /**
   * Remove tag from customer
   */
  static async removeCustomerTag(customerId: string, tag: string): Promise<void> {
    await apiClient.delete(
      `${config.endpoints.customers?.base || '/customers'}/${customerId}/tags/${encodeURIComponent(tag)}`
    );
  }

  /**
   * Export customers data
   */
  static async exportCustomers(filters?: CustomerFilters, format: 'csv' | 'xlsx' = 'csv'): Promise<Blob> {
    const response = await apiClient.get(
      `${config.endpoints.customers?.base || '/customers'}/export`,
      { 
        params: { ...filters, format },
        responseType: 'blob'
      }
    );
    return response.data;
  }

  /**
   * Import customers from file
   */
  static async importCustomers(file: File): Promise<{
    imported: number;
    errors: string[];
  }> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await apiClient.post<{
      success: boolean;
      data: {
        imported: number;
        errors: string[];
      };
      message: string;
    }>(
      `${config.endpoints.customers?.base || '/customers'}/import`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data.data;
  }
}
