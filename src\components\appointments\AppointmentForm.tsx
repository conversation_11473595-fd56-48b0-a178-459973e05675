import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Button from '../ui/button/Button';
import Label from '../form/Label';
import Input from '../form/input/InputField';
import { ErrorDisplay } from '../error';
import { useCreateAppointment, useUpdateAppointment } from '../../hooks/useAppointments';
import { useServices } from '../../hooks/useServices';
import { useLocations } from '../../hooks/useLocations';
import { useQueues } from '../../hooks/useQueues';
import { useProviderCustomers, useCreateProviderCustomer } from '../../hooks/useProviderCustomers';
import { Appointment } from '../../types';
import { ProviderCustomer } from '../../types/provider-customer';
import { localDateTimeToUTC, utcToLocalDateTime } from '../../utils/timezone';
import SearchableSelect, { SearchableSelectOption } from '../ui/SearchableSelect';

// Validation schema based on documented API
const appointmentSchema = z.object({
  customerUserId: z.string().min(1, "Please select a customer"),
  serviceId: z.coerce.number().min(1, "Please select a service"),
  placeId: z.coerce.number().min(1, "Please select a location"),
  queueId: z.coerce.number().min(1, "Please select a queue"),
  expectedAppointmentStartTime: z.string().min(1, "Please select start time"),
  expectedAppointmentEndTime: z.string().min(1, "End time is required"),
  serviceDuration: z.coerce.number().min(1, "Service duration is required"),
  notes: z.string().optional(),
  slots: z.coerce.number().min(1, "At least 1 slot is required").default(1),
  // Customer creation fields
  customerFirstName: z.string().optional(),
  customerLastName: z.string().optional(),
  customerEmail: z.union([z.string().email(), z.literal('')]).optional(),
  customerPhone: z.string().optional(),
});

type AppointmentFormData = z.infer<typeof appointmentSchema>;

interface AppointmentFormProps {
  appointment?: Appointment | null;
  selectedDate?: string | null;
  onClose: () => void;
  onSuccess: () => void;
}

export default function AppointmentForm({ 
  appointment, 
  selectedDate, 
  onClose, 
  onSuccess 
}: AppointmentFormProps) {
  const [showNewCustomerForm, setShowNewCustomerForm] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<ProviderCustomer | null>(null);

  const isEditing = !!appointment;

  const { data: services } = useServices();
  const { data: locations } = useLocations();
  const { data: queues } = useQueues();
  const { data: customersResponse } = useProviderCustomers();

  const createAppointmentMutation = useCreateAppointment();
  const updateAppointmentMutation = useUpdateAppointment();
  const createCustomerMutation = useCreateProviderCustomer();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<AppointmentFormData>({
    resolver: zodResolver(appointmentSchema),
    defaultValues: {
      customerUserId: '',
      serviceId: 0,
      placeId: 0,
      queueId: 0,
      expectedAppointmentStartTime: '',
      expectedAppointmentEndTime: '',
      serviceDuration: 30,
      notes: '',
      slots: 1,
      customerFirstName: '',
      customerLastName: '',
      customerEmail: '',
      customerPhone: '',
    },
  });

  // Prepare customer options for the searchable select
  const customerOptions: SearchableSelectOption[] = customersResponse?.customers.map(customer => ({
    value: customer.id,
    label: `${customer.firstName} ${customer.lastName}`,
    subtitle: customer.email || customer.mobileNumber,
  })) || [];

  // Watch for service selection changes to auto-update duration
  const selectedServiceId = watch('serviceId');
  const currentStartTime = watch('expectedAppointmentStartTime');

  // Set initial date and time if provided
  useEffect(() => {
    if (selectedDate) {
      const startDate = new Date(selectedDate);

      // Check if the selectedDate includes meaningful time information
      // FullCalendar provides different formats:
      // - Month view: "2024-01-15" (date only)
      // - Week/Day view time slot: "2024-01-15T14:30:00" (with specific time)
      // - Week/Day view all-day: "2024-01-15T00:00:00" (midnight)

      const isDateOnly = !selectedDate.includes('T');
      const isMidnight = selectedDate.includes('T00:00:00');

      if (isDateOnly || isMidnight) {
        // If no meaningful time info, default to 9 AM
        startDate.setHours(9, 0, 0, 0);
      }
      // Otherwise, use the time from the slot click as-is

      const endDate = new Date(startDate);
      endDate.setMinutes(endDate.getMinutes() + 30); // Default 30-minute duration

      // Convert to local datetime format for the datetime-local input
      setValue('expectedAppointmentStartTime', utcToLocalDateTime(startDate.toISOString()));
      setValue('expectedAppointmentEndTime', utcToLocalDateTime(endDate.toISOString()));
    }
  }, [selectedDate, setValue]);

  // Auto-update duration and end time when service is selected
  useEffect(() => {
    if (selectedServiceId && services) {
      const selectedService = services.find(service => service.id === selectedServiceId);
      if (selectedService) {
        // Update the duration field
        setValue('serviceDuration', selectedService.duration);

        // Update end time if start time is already set
        if (currentStartTime) {
          const startDate = new Date(currentStartTime);
          const endDate = new Date(startDate);
          endDate.setMinutes(endDate.getMinutes() + selectedService.duration);
          setValue('expectedAppointmentEndTime', utcToLocalDateTime(endDate.toISOString()));
        }
      }
    }
  }, [selectedServiceId, services, currentStartTime, setValue]);

  // Auto-update end time when duration or start time changes
  const currentDuration = watch('serviceDuration');
  useEffect(() => {
    if (currentStartTime && currentDuration) {
      const startDate = new Date(currentStartTime);
      const endDate = new Date(startDate);
      endDate.setMinutes(endDate.getMinutes() + currentDuration);
      setValue('expectedAppointmentEndTime', utcToLocalDateTime(endDate.toISOString()));
    }
  }, [currentStartTime, currentDuration, setValue]);

  // Populate form when editing
  useEffect(() => {
    if (appointment) {
      const startTime = new Date(appointment.expectedAppointmentStartTime);
      const endTime = new Date(appointment.expectedAppointmentEndTime);
      const duration = Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60));

      reset({
        customerUserId: appointment.customerUserId || '',
        serviceId: appointment.serviceId || appointment.service?.id || 0,
        placeId: appointment.placeId || appointment.place?.id || 0,
        queueId: appointment.queueId || 0,
        expectedAppointmentStartTime: utcToLocalDateTime(appointment.expectedAppointmentStartTime),
        expectedAppointmentEndTime: utcToLocalDateTime(appointment.expectedAppointmentEndTime),
        serviceDuration: duration,
        notes: appointment.notes || '',
        slots: appointment.slots || 1,
      });
      // Find the customer in the provider customers list
      if (appointment.customerUserId && customersResponse?.customers) {
        const customer = customersResponse.customers.find(c => c.id === appointment.customerUserId);
        setSelectedCustomer(customer || null);
      }
    }
  }, [appointment, reset, customersResponse]);

  const handleCustomerSelect = (customerId: string, option: SearchableSelectOption) => {
    const customer = customersResponse?.customers.find(c => c.id === customerId);
    if (customer) {
      setSelectedCustomer(customer);
      setValue('customerUserId', customer.id);
      setShowNewCustomerForm(false);
    }
  };

  const handleCreateNewCustomer = async (customerData: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
  }) => {
    try {
      const newCustomer = await createCustomerMutation.mutateAsync({
        firstName: customerData.firstName,
        lastName: customerData.lastName,
        email: customerData.email,
        mobileNumber: customerData.phone || '',
      });
      setSelectedCustomer(newCustomer);
      setValue('customerUserId', newCustomer.id);
      setShowNewCustomerForm(false);
    } catch (error) {
      // Error handled by mutation
    }
  };

  const onSubmit = async (data: AppointmentFormData) => {
    try {
      // Create customer if needed
      if (showNewCustomerForm && data.customerFirstName && data.customerLastName && data.customerEmail) {
        await handleCreateNewCustomer({
          firstName: data.customerFirstName,
          lastName: data.customerLastName,
          email: data.customerEmail,
          phone: data.customerPhone,
        });
        return; // Customer creation will trigger form submission again
      }

      // Prepare appointment data according to API specification
      // Convert local datetime to UTC for API submission
      const appointmentData = {
        customerId: data.customerUserId, // API expects 'customerId' not 'customerUserId'
        serviceId: data.serviceId,
        placeId: data.placeId,
        queueId: data.queueId,
        expectedStartTime: localDateTimeToUTC(data.expectedAppointmentStartTime), // API expects 'expectedStartTime'
        expectedEndTime: localDateTimeToUTC(data.expectedAppointmentEndTime), // API expects 'expectedEndTime'
        serviceDuration: data.serviceDuration,
        notes: data.notes,
        slots: data.slots,
      };



      if (isEditing && appointment) {
        await updateAppointmentMutation.mutateAsync({
          id: appointment.id,
          data: appointmentData,
        });
      } else {
        await createAppointmentMutation.mutateAsync(appointmentData);
      }

      onSuccess();
    } catch (error) {
      // Error handled by mutations
    }
  };

  const isLoading = createAppointmentMutation.isPending || 
                   updateAppointmentMutation.isPending || 
                   createCustomerMutation.isPending;
  
  const error = createAppointmentMutation.error || 
                updateAppointmentMutation.error || 
                createCustomerMutation.error;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-3xl overflow-hidden">
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          {isEditing ? 'Edit Appointment' : 'New Appointment'}
        </h2>
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
          {isEditing ? 'Update appointment details' : 'Schedule a new appointment'}
        </p>
      </div>

      <div className="p-6">

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Customer Selection */}
        <div>
          <Label>
            Customer <span className="text-red-500">*</span>
          </Label>

          <SearchableSelect
            options={customerOptions}
            value={selectedCustomer?.id || ''}
            placeholder="Select a customer..."
            searchPlaceholder="Search customers..."
            onSelect={handleCustomerSelect}
            onClear={() => {
              setSelectedCustomer(null);
              setValue('customerUserId', '');
            }}
            disabled={isLoading}
            loading={!customersResponse}
            error={errors.customerUserId?.message}
            allowClear={true}
            noOptionsMessage="No customers found"
            renderOption={(option) => (
              <div className="flex flex-col">
                <span className="font-medium text-gray-900 dark:text-white">
                  {option.label}
                </span>
                {option.subtitle && (
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {option.subtitle}
                  </span>
                )}
              </div>
            )}
            renderSelected={(option) => (
              <span className="font-medium text-gray-900 dark:text-white">
                {option.label}
              </span>
            )}
          />

          <div className="mt-3">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setShowNewCustomerForm(!showNewCustomerForm)}
              className="w-full"
            >
              {showNewCustomerForm ? 'Cancel' : 'Create New Customer'}
            </Button>
          </div>
        </div>

        {/* New Customer Form */}
        {showNewCustomerForm && (
          <div className="p-4 border border-gray-200 rounded-lg dark:border-gray-700 space-y-4">
            <h3 className="font-medium text-gray-900 dark:text-white">
              New Customer Information
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>First Name *</Label>
                <Input
                  {...register('customerFirstName')}
                  placeholder="Enter first name"
                  disabled={isLoading}
                />
              </div>
              <div>
                <Label>Last Name *</Label>
                <Input
                  {...register('customerLastName')}
                  placeholder="Enter last name"
                  disabled={isLoading}
                />
              </div>
            </div>
            
            <div>
              <Label>Email *</Label>
              <Input
                {...register('customerEmail')}
                type="email"
                placeholder="Enter email address"
                disabled={isLoading}
              />
            </div>
            
            <div>
              <Label>Phone</Label>
              <Input
                {...register('customerPhone')}
                placeholder="Enter phone number"
                disabled={isLoading}
              />
            </div>
          </div>
        )}

        {/* Service Selection */}
        <div>
          <Label>
            Service <span className="text-red-500">*</span>
          </Label>
          <select
            {...register('serviceId', { valueAsNumber: true })}
            disabled={isLoading}
            className="w-full h-11 rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:focus:border-brand-800"
          >
            <option value="">Select a service</option>
            {services?.map((service) => (
              <option key={service.id} value={service.id}>
                {service.title} - ${service.price} ({service.duration}min)
              </option>
            ))}
          </select>
          {errors.serviceId && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.serviceId.message}
            </p>
          )}
        </div>

        {/* Location Selection */}
        <div>
          <Label>
            Location <span className="text-red-500">*</span>
          </Label>
          <select
            {...register('placeId', { valueAsNumber: true })}
            disabled={isLoading}
            className="w-full h-11 rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:focus:border-brand-800"
          >
            <option value="">Select a location</option>
            {locations?.map((location) => (
              <option key={location.id} value={location.id}>
                {location.name}
              </option>
            ))}
          </select>
          {errors.placeId && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.placeId.message}
            </p>
          )}
        </div>

        {/* Queue Selection */}
        <div>
          <Label>
            Queue <span className="text-red-500">*</span>
          </Label>
          <select
            {...register('queueId', { valueAsNumber: true })}
            disabled={isLoading}
            className="w-full h-11 rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:focus:border-brand-800"
          >
            <option value="">Select a queue</option>
            {queues?.map((queue) => (
              <option key={queue.id} value={queue.id}>
                {queue.title}
              </option>
            ))}
          </select>
          {errors.queueId && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.queueId.message}
            </p>
          )}
        </div>

        {/* Start Time */}
        <div>
          <Label>
            Start Time <span className="text-red-500">*</span>
          </Label>
          <Input
            {...register('expectedAppointmentStartTime')}
            type="datetime-local"
            disabled={isLoading}
          />
          {errors.expectedAppointmentStartTime && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.expectedAppointmentStartTime.message}
            </p>
          )}
        </div>

        {/* End Time */}
        <div>
          <Label>
            End Time <span className="text-red-500">*</span>
          </Label>
          <Input
            {...register('expectedAppointmentEndTime')}
            type="datetime-local"
            disabled={isLoading}
          />
          {errors.expectedAppointmentEndTime && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.expectedAppointmentEndTime.message}
            </p>
          )}
        </div>

        {/* Duration and Slots */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label>
              Duration (minutes) <span className="text-red-500">*</span>
              <span className="text-sm text-gray-500 dark:text-gray-400 font-normal ml-1">(Auto-set from service)</span>
            </Label>
            <Input
              {...register('serviceDuration', { valueAsNumber: true })}
              type="number"
              min="1"
              disabled={isLoading}
              readOnly
              className="bg-gray-50 dark:bg-gray-800 cursor-not-allowed"
              placeholder="Select a service to set duration"
            />
            {errors.serviceDuration && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.serviceDuration.message}
              </p>
            )}
          </div>

          <div>
            <Label>
              Slots <span className="text-red-500">*</span>
            </Label>
            <Input
              {...register('slots', { valueAsNumber: true })}
              type="number"
              min="1"
              disabled={isLoading}
            />
            {errors.slots && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.slots.message}
              </p>
            )}
          </div>
        </div>

        {/* Notes */}
        <div>
          <Label>Notes</Label>
          <textarea
            {...register('notes')}
            rows={3}
            placeholder="Add any notes or special instructions..."
            disabled={isLoading}
            className="w-full rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs placeholder:text-gray-400 focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30 dark:focus:border-brand-800"
          />
        </div>

        {error && (
          <ErrorDisplay
            error={error}
            variant="banner"
            size="sm"
          />
        )}



        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isLoading}
          >
            {isLoading 
              ? (isEditing ? 'Updating...' : 'Creating...') 
              : (isEditing ? 'Update Appointment' : 'Create Appointment')
            }
          </Button>
        </div>
      </form>
      </div>
    </div>
  );
}
