import React, { useState, useEffect } from 'react';
import { useFeatureFlags } from '../../hooks/useFeatureFlags';
import Button from '../ui/button/Button';

interface RolloutMetrics {
  totalUsers: number;
  enabledUsers: number;
  algerianUsers: number;
  betaUsers: number;
  successRate: number;
  errorRate: number;
  conversionRate: number;
}

interface RolloutControlProps {
  onRolloutChange?: (percentage: number) => void;
  className?: string;
}

/**
 * Admin component for controlling Chargily Pay rollout
 */
export const RolloutControl: React.FC<RolloutControlProps> = ({
  onRolloutChange,
  className = '',
}) => {
  const { flags, override } = useFeatureFlags();
  const [rolloutPercentage, setRolloutPercentage] = useState(0);
  const [rolloutPhase, setRolloutPhase] = useState<'disabled' | 'beta' | 'algerian' | 'gradual' | 'full'>('disabled');
  const [metrics, setMetrics] = useState<RolloutMetrics>({
    totalUsers: 0,
    enabledUsers: 0,
    algerianUsers: 0,
    betaUsers: 0,
    successRate: 0,
    errorRate: 0,
    conversionRate: 0,
  });

  // Simulate metrics (in real app, this would come from analytics API)
  useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(prev => ({
        ...prev,
        totalUsers: Math.floor(Math.random() * 1000) + 500,
        enabledUsers: Math.floor(Math.random() * 200) + 50,
        algerianUsers: Math.floor(Math.random() * 100) + 20,
        betaUsers: Math.floor(Math.random() * 50) + 10,
        successRate: Math.random() * 100,
        errorRate: Math.random() * 5,
        conversionRate: Math.random() * 15 + 5,
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const handlePhaseChange = (phase: typeof rolloutPhase) => {
    setRolloutPhase(phase);
    
    switch (phase) {
      case 'disabled':
        override('CHARGILY_PAYMENT', false);
        override('GRADUAL_ROLLOUT', false);
        override('BETA_USERS_ONLY', false);
        override('ALGERIAN_USERS_ONLY', false);
        setRolloutPercentage(0);
        break;
        
      case 'beta':
        override('CHARGILY_PAYMENT', true);
        override('BETA_USERS_ONLY', true);
        override('ALGERIAN_USERS_ONLY', false);
        override('GRADUAL_ROLLOUT', false);
        setRolloutPercentage(0);
        break;
        
      case 'algerian':
        override('CHARGILY_PAYMENT', true);
        override('ALGERIAN_USERS_ONLY', true);
        override('BETA_USERS_ONLY', false);
        override('GRADUAL_ROLLOUT', false);
        setRolloutPercentage(0);
        break;
        
      case 'gradual':
        override('CHARGILY_PAYMENT', true);
        override('GRADUAL_ROLLOUT', true);
        override('BETA_USERS_ONLY', false);
        override('ALGERIAN_USERS_ONLY', false);
        break;
        
      case 'full':
        override('CHARGILY_PAYMENT', true);
        override('GRADUAL_ROLLOUT', false);
        override('BETA_USERS_ONLY', false);
        override('ALGERIAN_USERS_ONLY', false);
        setRolloutPercentage(100);
        break;
    }
    
    onRolloutChange?.(rolloutPercentage);
  };

  const handlePercentageChange = (percentage: number) => {
    setRolloutPercentage(percentage);
    onRolloutChange?.(percentage);
  };

  const getRolloutStatus = () => {
    if (!flags.CHARGILY_PAYMENT) return 'Disabled';
    if (flags.BETA_USERS_ONLY) return 'Beta Users Only';
    if (flags.ALGERIAN_USERS_ONLY) return 'Algerian Users Only';
    if (flags.GRADUAL_ROLLOUT) return `Gradual Rollout (${rolloutPercentage}%)`;
    return 'Full Rollout';
  };

  const getStatusColor = () => {
    if (!flags.CHARGILY_PAYMENT) return 'text-gray-600';
    if (flags.BETA_USERS_ONLY) return 'text-blue-600';
    if (flags.ALGERIAN_USERS_ONLY) return 'text-green-600';
    if (flags.GRADUAL_ROLLOUT) return 'text-yellow-600';
    return 'text-green-600';
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Chargily Pay Rollout Control
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Manage the gradual rollout of Chargily Pay integration
          </p>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-600 dark:text-gray-400">Current Status</p>
          <p className={`font-semibold ${getStatusColor()}`}>
            {getRolloutStatus()}
          </p>
        </div>
      </div>

      {/* Rollout Phase Selection */}
      <div className="mb-6">
        <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3">
          Rollout Phase
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-2">
          {[
            { phase: 'disabled', label: 'Disabled', description: 'Feature completely disabled' },
            { phase: 'beta', label: 'Beta Only', description: 'Beta users only' },
            { phase: 'algerian', label: 'Algerian Only', description: 'Algerian users only' },
            { phase: 'gradual', label: 'Gradual', description: 'Percentage-based rollout' },
            { phase: 'full', label: 'Full Rollout', description: 'All users enabled' },
          ].map(({ phase, label, description }) => (
            <button
              key={phase}
              onClick={() => handlePhaseChange(phase as any)}
              className={`p-3 rounded border text-left transition-colors ${
                rolloutPhase === phase
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-blue-300'
              }`}
            >
              <p className="font-medium text-gray-900 dark:text-white text-sm">
                {label}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {description}
              </p>
            </button>
          ))}
        </div>
      </div>

      {/* Gradual Rollout Percentage */}
      {rolloutPhase === 'gradual' && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-md font-semibold text-gray-900 dark:text-white">
              Rollout Percentage
            </h4>
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {rolloutPercentage}%
            </span>
          </div>
          <input
            type="range"
            min="0"
            max="100"
            step="5"
            value={rolloutPercentage}
            onChange={(e) => handlePercentageChange(parseInt(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
          />
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
            <span>0%</span>
            <span>25%</span>
            <span>50%</span>
            <span>75%</span>
            <span>100%</span>
          </div>
        </div>
      )}

      {/* Metrics Dashboard */}
      <div className="mb-6">
        <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3">
          Rollout Metrics
        </h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-gray-50 dark:bg-gray-700 rounded p-3">
            <p className="text-xs text-gray-600 dark:text-gray-400">Total Users</p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              {metrics.totalUsers.toLocaleString()}
            </p>
          </div>
          <div className="bg-gray-50 dark:bg-gray-700 rounded p-3">
            <p className="text-xs text-gray-600 dark:text-gray-400">Enabled Users</p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              {metrics.enabledUsers.toLocaleString()}
            </p>
          </div>
          <div className="bg-gray-50 dark:bg-gray-700 rounded p-3">
            <p className="text-xs text-gray-600 dark:text-gray-400">Success Rate</p>
            <p className="text-lg font-semibold text-green-600">
              {metrics.successRate.toFixed(1)}%
            </p>
          </div>
          <div className="bg-gray-50 dark:bg-gray-700 rounded p-3">
            <p className="text-xs text-gray-600 dark:text-gray-400">Error Rate</p>
            <p className="text-lg font-semibold text-red-600">
              {metrics.errorRate.toFixed(1)}%
            </p>
          </div>
        </div>
      </div>

      {/* Feature Flags Status */}
      <div className="mb-6">
        <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3">
          Feature Flags Status
        </h4>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {[
            { flag: 'CHARGILY_PAYMENT', label: 'Chargily Payment' },
            { flag: 'CHARGILY_AUTO_SELECTION', label: 'Auto Selection' },
            { flag: 'AUTO_DETECT_LOCATION', label: 'Location Detection' },
            { flag: 'BETA_USERS_ONLY', label: 'Beta Users Only' },
            { flag: 'ALGERIAN_USERS_ONLY', label: 'Algerian Users Only' },
            { flag: 'GRADUAL_ROLLOUT', label: 'Gradual Rollout' },
          ].map(({ flag, label }) => (
            <div key={flag} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
              <span className="text-sm text-gray-900 dark:text-white">{label}</span>
              <span className={`text-xs font-medium px-2 py-1 rounded ${
                flags[flag as keyof typeof flags]
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
              }`}>
                {flags[flag as keyof typeof flags] ? 'ON' : 'OFF'}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Emergency Controls */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
        <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3">
          Emergency Controls
        </h4>
        <div className="flex space-x-3">
          <Button
            onClick={() => handlePhaseChange('disabled')}
            variant="outline"
            size="sm"
            className="border-red-300 text-red-700 hover:bg-red-50"
          >
            Emergency Stop
          </Button>
          <Button
            onClick={() => handlePhaseChange('algerian')}
            variant="outline"
            size="sm"
            className="border-yellow-300 text-yellow-700 hover:bg-yellow-50"
          >
            Algerian Only
          </Button>
          <Button
            onClick={() => handlePhaseChange('full')}
            variant="outline"
            size="sm"
            className="border-green-300 text-green-700 hover:bg-green-50"
          >
            Full Rollout
          </Button>
        </div>
      </div>
    </div>
  );
};

export default RolloutControl;
