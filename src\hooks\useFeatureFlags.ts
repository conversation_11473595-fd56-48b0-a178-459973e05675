import { useState, useEffect } from 'react';
import { featureFlags, FeatureFlags } from '../lib/feature-flags';

/**
 * React hook for feature flags
 */
export const useFeatureFlags = () => {
  const [flags, setFlags] = useState<FeatureFlags>(featureFlags.getAllFlags());

  useEffect(() => {
    const unsubscribe = featureFlags.subscribe(setFlags);
    return unsubscribe;
  }, []);

  return {
    flags,
    isEnabled: (flag: keyof FeatureFlags) => flags[flag],
    updateFlags: featureFlags.updateFlags.bind(featureFlags),
    override: featureFlags.override.bind(featureFlags),
  };
};

/**
 * Hook for checking if a specific feature is enabled
 */
export const useFeatureFlag = (flag: keyof FeatureFlags) => {
  const { isEnabled } = useFeatureFlags();
  return isEnabled(flag);
};

/**
 * Hook for checking multiple feature flags
 */
export const useFeatureFlags_Multiple = (
  flags: Array<keyof FeatureFlags>,
  mode: 'all' | 'any' = 'all'
) => {
  const { isEnabled } = useFeatureFlags();
  
  const result = mode === 'all' 
    ? flags.every(flag => isEnabled(flag))
    : flags.some(flag => isEnabled(flag));
    
  return result;
};
