import { apiClient, handleApiResponse, ApiResponse } from './api-client';
import { config } from './config';

/**
 * Generic API service class for CRUD operations
 */
export class ApiService {
  constructor(private baseEndpoint: string) {}

  async getAll<T>(params?: Record<string, any>): Promise<T[]> {
    const response = await apiClient.get<ApiResponse<T[]>>(this.baseEndpoint, { params });
    return handleApiResponse(response);
  }

  async getById<T>(id: string | number): Promise<T> {
    const response = await apiClient.get<ApiResponse<T>>(`${this.baseEndpoint}/${id}`);
    return handleApiResponse(response);
  }

  async create<T>(data: Partial<T>): Promise<T> {
    const response = await apiClient.post<ApiResponse<T>>(this.baseEndpoint, data);
    return handleApiResponse(response);
  }

  async update<T>(id: string | number, data: Partial<T>): Promise<T> {
    const response = await apiClient.put<ApiResponse<T>>(`${this.baseEndpoint}/${id}`, data);
    return handleApiResponse(response);
  }

  async delete(id: string | number): Promise<void> {
    const response = await apiClient.delete<ApiResponse<void>>(`${this.baseEndpoint}/${id}`);
    handleApiResponse(response);
  }
}

/**
 * Retry utility for failed requests
 */
export const retryRequest = async <T>(
  requestFn: () => Promise<T>,
  maxAttempts: number = config.api.retryAttempts,
  delay: number = config.api.retryDelay
): Promise<T> => {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await requestFn();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxAttempts) {
        throw lastError;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw lastError!;
};

/**
 * Utility to build query parameters
 */
export const buildQueryParams = (params: Record<string, any>): string => {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (Array.isArray(value)) {
        value.forEach(item => searchParams.append(key, String(item)));
      } else {
        searchParams.append(key, String(value));
      }
    }
  });

  return searchParams.toString();
};

/**
 * File upload utility
 */
export const uploadFile = async (
  endpoint: string,
  file: File,
  additionalData?: Record<string, any>
): Promise<any> => {
  const formData = new FormData();
  formData.append('file', file);

  if (additionalData) {
    Object.entries(additionalData).forEach(([key, value]) => {
      formData.append(key, String(value));
    });
  }

  const response = await apiClient.post<ApiResponse<any>>(endpoint, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return handleApiResponse(response);
};

/**
 * Download file utility
 */
export const downloadFile = async (url: string, filename?: string): Promise<void> => {
  const response = await apiClient.get(url, {
    responseType: 'blob',
  });

  const blob = new Blob([response.data]);
  const downloadUrl = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = downloadUrl;
  link.download = filename || 'download';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(downloadUrl);
};

/**
 * Debounce utility for search/filter operations
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

/**
 * Format API errors for display
 */
export const formatApiError = (error: any): string => {
  if (typeof error === 'string') {
    return error;
  }

  if (error?.message) {
    return error.message;
  }

  if (error?.errors && Array.isArray(error.errors)) {
    return error.errors.join(', ');
  }

  return 'An unexpected error occurred';
};
