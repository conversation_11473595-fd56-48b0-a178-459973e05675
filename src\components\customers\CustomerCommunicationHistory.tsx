import React, { useState } from 'react';
import { ProviderCustomer } from '../../types/provider-customer';
import { formatLocalDateTime } from '../../utils/timezone';
import { 
  ChatIcon, 
  PhoneIcon, 
  EnvelopeIcon, 
  CalendarIcon, 
  ClockIcon,
  DocumentTextIcon,
  UserCircleIcon
} from '../../icons';

interface CommunicationEntry {
  id: string;
  type: 'note' | 'call' | 'email' | 'sms' | 'appointment' | 'system';
  title: string;
  content: string;
  timestamp: string;
  author?: string;
  metadata?: {
    appointmentId?: string;
    duration?: number;
    status?: string;
  };
}

interface CustomerCommunicationHistoryProps {
  customer: ProviderCustomer;
  className?: string;
}

export default function CustomerCommunicationHistory({ 
  customer, 
  className = '' 
}: CustomerCommunicationHistoryProps) {
  const [newNote, setNewNote] = useState('');
  const [isAddingNote, setIsAddingNote] = useState(false);

  // Mock communication history - in a real app, this would come from an API
  const [communications, setCommunications] = useState<CommunicationEntry[]>([
    {
      id: '1',
      type: 'appointment',
      title: 'Appointment Completed',
      content: 'Regular checkup appointment completed successfully. Customer was satisfied with the service.',
      timestamp: '2024-01-20T14:30:00Z',
      author: 'System',
      metadata: {
        appointmentId: 'apt_123',
        status: 'completed'
      }
    },
    {
      id: '2',
      type: 'note',
      title: 'Customer Preference Note',
      content: 'Customer prefers morning appointments and has requested to be notified 24 hours in advance.',
      timestamp: '2024-01-18T10:15:00Z',
      author: 'Dr. Smith'
    },
    {
      id: '3',
      type: 'call',
      title: 'Follow-up Call',
      content: 'Called customer to confirm next appointment. Customer confirmed availability.',
      timestamp: '2024-01-15T16:45:00Z',
      author: 'Reception',
      metadata: {
        duration: 5
      }
    },
    {
      id: '4',
      type: 'email',
      title: 'Appointment Reminder Sent',
      content: 'Automated reminder email sent for upcoming appointment.',
      timestamp: '2024-01-14T09:00:00Z',
      author: 'System'
    },
    {
      id: '5',
      type: 'sms',
      title: 'SMS Confirmation',
      content: 'SMS confirmation sent for appointment booking.',
      timestamp: '2024-01-10T11:30:00Z',
      author: 'System'
    }
  ]);

  const getTypeIcon = (type: CommunicationEntry['type']) => {
    const iconProps = { className: "w-4 h-4" };
    
    switch (type) {
      case 'note':
        return <DocumentTextIcon {...iconProps} />;
      case 'call':
        return <PhoneIcon {...iconProps} />;
      case 'email':
        return <EnvelopeIcon {...iconProps} />;
      case 'sms':
        return <ChatIcon {...iconProps} />;
      case 'appointment':
        return <CalendarIcon {...iconProps} />;
      case 'system':
        return <ClockIcon {...iconProps} />;
      default:
        return <DocumentTextIcon {...iconProps} />;
    }
  };

  const getTypeColor = (type: CommunicationEntry['type']) => {
    switch (type) {
      case 'note':
        return 'bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400';
      case 'call':
        return 'bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400';
      case 'email':
        return 'bg-purple-100 text-purple-600 dark:bg-purple-900/20 dark:text-purple-400';
      case 'sms':
        return 'bg-orange-100 text-orange-600 dark:bg-orange-900/20 dark:text-orange-400';
      case 'appointment':
        return 'bg-indigo-100 text-indigo-600 dark:bg-indigo-900/20 dark:text-indigo-400';
      case 'system':
        return 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400';
      default:
        return 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.abs(now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return formatLocalDateTime(timestamp, {
        hour: '2-digit',
        minute: '2-digit'
      });
    } else if (diffInHours < 24 * 7) {
      return formatLocalDateTime(timestamp, {
        weekday: 'short',
        hour: '2-digit',
        minute: '2-digit'
      });
    } else {
      return formatLocalDateTime(timestamp, {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  const handleAddNote = () => {
    if (!newNote.trim()) return;

    const note: CommunicationEntry = {
      id: Date.now().toString(),
      type: 'note',
      title: 'Provider Note',
      content: newNote.trim(),
      timestamp: new Date().toISOString(),
      author: 'Current User' // In a real app, this would be the logged-in user
    };

    setCommunications(prev => [note, ...prev]);
    setNewNote('');
    setIsAddingNote(false);
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Communication History
        </h3>
        <button
          onClick={() => setIsAddingNote(!isAddingNote)}
          className="text-sm text-brand-600 dark:text-brand-400 hover:text-brand-700 dark:hover:text-brand-300"
        >
          {isAddingNote ? 'Cancel' : 'Add Note'}
        </button>
      </div>

      {/* Add Note Form */}
      {isAddingNote && (
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <textarea
            value={newNote}
            onChange={(e) => setNewNote(e.target.value)}
            placeholder="Add a note about this customer..."
            rows={3}
            className="w-full rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 placeholder:text-gray-400 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder:text-gray-500"
          />
          <div className="flex items-center justify-end space-x-2 mt-3">
            <button
              onClick={() => {
                setIsAddingNote(false);
                setNewNote('');
              }}
              className="px-3 py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
            >
              Cancel
            </button>
            <button
              onClick={handleAddNote}
              disabled={!newNote.trim()}
              className="px-3 py-1 text-sm bg-brand-600 text-white rounded-lg hover:bg-brand-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Add Note
            </button>
          </div>
        </div>
      )}

      {/* Communication Timeline */}
      <div className="space-y-3">
        {communications.length > 0 ? (
          communications.map((comm, index) => (
            <div
              key={comm.id}
              className="flex items-start space-x-3 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
            >
              {/* Icon */}
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getTypeColor(comm.type)}`}>
                {getTypeIcon(comm.type)}
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                    {comm.title}
                  </h4>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {formatTimestamp(comm.timestamp)}
                  </span>
                </div>
                
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                  {comm.content}
                </p>

                <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                  {comm.author && (
                    <div className="flex items-center space-x-1">
                      <UserCircleIcon className="w-3 h-3" />
                      <span>{comm.author}</span>
                    </div>
                  )}
                  
                  {comm.metadata?.duration && (
                    <div className="flex items-center space-x-1">
                      <ClockIcon className="w-3 h-3" />
                      <span>{comm.metadata.duration} min</span>
                    </div>
                  )}
                  
                  {comm.metadata?.status && (
                    <span className={`px-2 py-0.5 rounded-full text-xs ${
                      comm.metadata.status === 'completed' 
                        ? 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400'
                        : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
                    }`}>
                      {comm.metadata.status}
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-8">
            <ChatIcon className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-1">
              No communication history
            </h4>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Start by adding a note about this customer.
            </p>
          </div>
        )}
      </div>

      {/* Summary Stats */}
      {communications.length > 0 && (
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
            Communication Summary
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            {['note', 'call', 'email', 'appointment'].map((type) => {
              const count = communications.filter(c => c.type === type).length;
              return (
                <div key={type} className="text-center">
                  <div className="text-lg font-bold text-gray-900 dark:text-white">
                    {count}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 capitalize">
                    {type}s
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
