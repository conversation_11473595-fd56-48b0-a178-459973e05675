import React, { useState } from "react";
import { <PERSON>, useNavigate } from "react-router";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { ChevronLeftIcon, EyeCloseIcon, EyeIcon } from "../../icons";
import Label from "../form/Label";
import Input from "../form/input/InputField";
import Checkbox from "../form/input/Checkbox";
import Button from "../ui/button/Button";
import { ErrorDisplay } from "../error";
import { useProviderCategories, useRequestEmailOtp, useRequestPhoneOtp } from "../../hooks/useAuthMutations";
import { OtpRequest } from "../../types";
import TwoStepCategorySelector from "../form/TwoStepCategorySelector";

// Validation schema
const registrationSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  phone: z.string().min(10, "Please enter a valid phone number"),
  businessName: z.string().min(2, "Business name must be at least 2 characters"),
  password: z.string()
    .min(8, "Password must be at least 8 characters")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, "Password must contain at least one uppercase letter, one lowercase letter, and one number"),
  confirmPassword: z.string(),
  providerCategoryId: z.number().min(1, "Please select a specific service category").refine(
    (value) => {
      // This will be validated by the component itself
      return value > 0;
    },
    "Please complete both category selection steps"
  ),
  verificationType: z.enum(["email", "phone"]),
  agreeToTerms: z.boolean().refine(val => val === true, "You must agree to the terms and conditions"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type RegistrationFormData = z.infer<typeof registrationSchema>;

export default function SignUpForm() {
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
    setValue,
  } = useForm<RegistrationFormData>({
    resolver: zodResolver(registrationSchema),
    defaultValues: {
      verificationType: "email",
      agreeToTerms: false,
    },
  });

  const { data: categories, isLoading: categoriesLoading } = useProviderCategories();
  const requestEmailOtpMutation = useRequestEmailOtp();
  const requestPhoneOtpMutation = useRequestPhoneOtp();
  const verificationType = watch("verificationType");
  const isLoading = isSubmitting || requestEmailOtpMutation.isPending || requestPhoneOtpMutation.isPending;
  const onSubmit = async (data: RegistrationFormData) => {
    try {
      const otpData: OtpRequest = {
        firstName: data.firstName,
        lastName: data.lastName,
        password: data.password,
        isProviderRegistration: true,
        providerCategoryId: data.providerCategoryId,
        businessName: data.businessName,
        phone: data.phone,
        ...(data.verificationType === "email"
          ? { email: data.email }
          : { phoneNumber: data.phone }
        ),
      };

      if (data.verificationType === "email") {
        await requestEmailOtpMutation.mutateAsync(otpData);
      } else {
        await requestPhoneOtpMutation.mutateAsync(otpData);
      }

      // Navigate to OTP verification with registration data
      const registrationData = {
        identifier: data.verificationType === "email" ? data.email : data.phone,
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        password: data.password,
        providerCategoryId: data.providerCategoryId,
        businessName: data.businessName,
        phone: data.phone,
        verificationType: data.verificationType,
      };



      navigate("/verify-otp", {
        state: {
          registrationData,
        },
      });
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  return (
    <div className="flex flex-col flex-1 w-full overflow-y-auto lg:w-1/2 no-scrollbar">
      <div className="w-full max-w-md mx-auto mb-5 sm:pt-10">
        <Link
          to="/"
          className="inline-flex items-center text-sm text-gray-500 transition-colors hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
        >
          <ChevronLeftIcon className="size-5" />
          Back to dashboard
        </Link>
      </div>
      <div className="flex flex-col justify-center flex-1 w-full max-w-md mx-auto">
        <div>
          <div className="mb-5 sm:mb-8">
            <h1 className="mb-2 font-semibold text-gray-800 text-title-sm dark:text-white/90 sm:text-title-md">
              Provider Registration
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Create your provider account to start managing your business!
            </p>
          </div>
          <div>
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="space-y-5">
                <div className="grid grid-cols-1 gap-5 sm:grid-cols-2">
                  <div className="sm:col-span-1">
                    <Label>
                      First Name<span className="text-error-500">*</span>
                    </Label>
                    <Input
                      {...register("firstName")}
                      type="text"
                      placeholder="Enter your first name"
                      disabled={isLoading}
                    />
                    {errors.firstName && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                        {errors.firstName.message}
                      </p>
                    )}
                  </div>
                  <div className="sm:col-span-1">
                    <Label>
                      Last Name<span className="text-error-500">*</span>
                    </Label>
                    <Input
                      {...register("lastName")}
                      type="text"
                      placeholder="Enter your last name"
                      disabled={isLoading}
                    />
                    {errors.lastName && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                        {errors.lastName.message}
                      </p>
                    )}
                  </div>
                </div>

                <div>
                  <Label>
                    Business Name<span className="text-error-500">*</span>
                  </Label>
                  <Input
                    {...register("businessName")}
                    type="text"
                    placeholder="Enter your business name"
                    disabled={isLoading}
                  />
                  {errors.businessName && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {errors.businessName.message}
                    </p>
                  )}
                </div>

                <TwoStepCategorySelector
                  categories={categories || []}
                  value={watch("providerCategoryId") || undefined}
                  onChange={(categoryId) => {
                    setValue("providerCategoryId", categoryId || 0);
                  }}
                  disabled={isLoading || categoriesLoading}
                  error={errors.providerCategoryId?.message}
                  required={true}
                />

                <div>
                  <Label>
                    Email<span className="text-error-500">*</span>
                  </Label>
                  <Input
                    {...register("email")}
                    type="email"
                    placeholder="Enter your email"
                    disabled={isLoading}
                  />
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {errors.email.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>
                    Phone Number<span className="text-error-500">*</span>
                  </Label>
                  <Input
                    {...register("phone")}
                    type="tel"
                    placeholder="Enter your phone number"
                    disabled={isLoading}
                  />
                  {errors.phone && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {errors.phone.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>
                    Password<span className="text-error-500">*</span>
                  </Label>
                  <div className="relative">
                    <Input
                      {...register("password")}
                      type={showPassword ? "text" : "password"}
                      placeholder="Create a strong password"
                      disabled={isLoading}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
                    >
                      {showPassword ? (
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                        </svg>
                      ) : (
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      )}
                    </button>
                  </div>
                  {errors.password && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {errors.password.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>
                    Confirm Password<span className="text-error-500">*</span>
                  </Label>
                  <Input
                    {...register("confirmPassword")}
                    type="password"
                    placeholder="Confirm your password"
                    disabled={isLoading}
                  />
                  {errors.confirmPassword && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {errors.confirmPassword.message}
                    </p>
                  )}
                </div>

                {/* <div>
                  <Label>
                    Verification Method<span className="text-error-500">*</span>
                  </Label>
                  <div className="grid grid-cols-2 gap-3 mt-2">
                    <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800">
                      <input
                        {...register("verificationType")}
                        type="radio"
                        value="email"
                        className="mr-3 text-brand-500 focus:ring-brand-500"
                        disabled={isLoading}
                      />
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        Verify by Email
                      </span>
                    </label>
                    <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800">
                      <input
                        {...register("verificationType")}
                        type="radio"
                        value="phone"
                        className="mr-3 text-brand-500 focus:ring-brand-500"
                        disabled={isLoading}
                      />
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        Verify by SMS
                      </span>
                    </label>
                  </div>
                  {errors.verificationType && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {errors.verificationType.message}
                    </p>
                  )}
                </div> */}

                <div className="flex items-start gap-3">
                  <Checkbox
                    className="w-5 h-5 mt-0.5"
                    checked={watch("agreeToTerms")}
                    onChange={(checked) => setValue("agreeToTerms", checked)}
                    disabled={isLoading}
                  />
                  <p className="text-sm font-normal text-gray-500 dark:text-gray-400">
                    By creating an account you agree to the{" "}
                    <span className="text-gray-800 dark:text-white/90">
                      Terms and Conditions
                    </span>{" "}
                    and our{" "}
                    <span className="text-gray-800 dark:text-white">
                      Privacy Policy
                    </span>
                  </p>
                </div>
                {errors.agreeToTerms && (
                  <p className="text-sm text-red-600 dark:text-red-400">
                    {errors.agreeToTerms.message}
                  </p>
                )}

                {(requestEmailOtpMutation.error || requestPhoneOtpMutation.error) && (
                  <ErrorDisplay
                    error={requestEmailOtpMutation.error || requestPhoneOtpMutation.error}
                    variant="banner"
                    size="sm"
                  />
                )}

                <div>
                  <Button
                    type="submit"
                    className="w-full"
                    size="sm"
                    disabled={isLoading}
                  >
                    {isLoading ? "Sending verification code..." : "Continue"}
                  </Button>
                </div>
              </div>
            </form>

            <div className="mt-5">
              <p className="text-sm font-normal text-center text-gray-700 dark:text-gray-400 sm:text-start">
                Already have an account? {""}
                <Link
                  to="/signin"
                  className="text-brand-500 hover:text-brand-600 dark:text-brand-400"
                >
                  Sign In
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
