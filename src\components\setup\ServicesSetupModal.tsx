import React, { useState } from 'react';
import Button from '../ui/button/Button';
import { ErrorDisplay } from '../error';
import { useServices, useServiceCategories, useDeleteService } from '../../hooks/useServices';
import ServiceForm from '../services/ServiceForm';
import ServiceCard from '../services/ServiceCard';
import { Service, ServiceFilters } from '../../types';

interface ServicesSetupModalProps {
  onComplete: () => void;
  onCancel: () => void;
}

export default function ServicesSetupModal({ onComplete, onCancel }: ServicesSetupModalProps) {
  const [showServiceForm, setShowServiceForm] = useState(false);
  const [editingService, setEditingService] = useState<Service | null>(null);
  const [filters, setFilters] = useState<ServiceFilters>({});

  const { data: services, isLoading, error } = useServices(filters);
  const { data: categories } = useServiceCategories();
  const deleteServiceMutation = useDeleteService();

  const handleCreateService = () => {
    setEditingService(null);
    setShowServiceForm(true);
  };

  const handleEditService = (service: Service) => {
    setEditingService(service);
    setShowServiceForm(true);
  };

  const handleDeleteService = async (serviceId: number) => {
    if (window.confirm('Are you sure you want to delete this service?')) {
      try {
        await deleteServiceMutation.mutateAsync(serviceId);
      } catch (error) {
        // Error handled by mutation
      }
    }
  };

  const handleServiceFormClose = () => {
    setShowServiceForm(false);
    setEditingService(null);
  };

  const handleContinue = () => {
    if (services && services.length > 0) {
      onComplete();
    } else {
      alert('Please add at least one service before continuing.');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-6">
        <ErrorDisplay
          error={error}
          title="Failed to load services"
          variant="card"
          showRetry
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Add Your Services
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Add the services you offer to your customers. You can always add more services later.
        </p>
      </div>

      {/* Service Form */}
      {showServiceForm && (
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6 bg-gray-50 dark:bg-gray-800/50">
          <ServiceForm
            service={editingService}
            categories={categories}
            onSuccess={handleServiceFormClose}
            onCancel={handleServiceFormClose}
          />
        </div>
      )}

      {/* Add Service Button */}
      {!showServiceForm && (
        <div className="flex justify-center">
          <Button
            onClick={handleCreateService}
            className="flex items-center gap-2"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Service
          </Button>
        </div>
      )}

      {/* Services List */}
      {services && services.length > 0 && (
        <div>
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
            Your Services ({services.length})
          </h4>
          <div className="grid gap-4 md:grid-cols-2">
            {services.map((service) => (
              <ServiceCard
                key={service.id}
                service={service}
                onEdit={() => handleEditService(service)}
                onDelete={() => handleDeleteService(service.id)}
              />
            ))}
          </div>
        </div>
      )}

      {/* Empty State */}
      {services && services.length === 0 && !showServiceForm && (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
          <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No services yet
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Add your first service to get started with accepting appointments.
          </p>
          <Button onClick={handleCreateService}>
            Add Your First Service
          </Button>
        </div>
      )}

      {/* Progress Indicator */}
      {services && services.length > 0 && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 dark:bg-green-900/20 dark:border-green-800">
          <div className="flex items-center">
            <span className="text-green-500 mr-2 text-xl">✅</span>
            <div>
              <h4 className="text-green-800 dark:text-green-200 font-medium">
                Great! You have {services.length} service{services.length !== 1 ? 's' : ''} configured
              </h4>
              <p className="text-green-700 dark:text-green-300 text-sm">
                You can add more services anytime from your dashboard.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
        >
          Cancel
        </Button>
        <Button
          onClick={handleContinue}
          disabled={!services || services.length === 0}
        >
          Continue
        </Button>
      </div>
    </div>
  );
}
