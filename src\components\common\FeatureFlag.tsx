import React from 'react';
import { useFeatureFlags } from '../../hooks/useFeatureFlags';
import { FeatureFlags } from '../../lib/feature-flags';

interface FeatureFlagProps {
  flag: keyof FeatureFlags;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * Feature flag component wrapper
 */
export const FeatureFlag: React.FC<FeatureFlagProps> = ({ 
  flag, 
  children, 
  fallback = null 
}) => {
  const { isEnabled } = useFeatureFlags();
  
  return isEnabled(flag) ? <>{children}</> : <>{fallback}</>;
};

interface FeatureFlagsProps {
  flags: Array<keyof FeatureFlags>;
  mode?: 'all' | 'any';
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * Multiple feature flags component wrapper
 */
export const FeatureFlagsWrapper: React.FC<FeatureFlagsProps> = ({ 
  flags, 
  mode = 'all', 
  children, 
  fallback = null 
}) => {
  const { isEnabled } = useFeatureFlags();
  
  const shouldShow = mode === 'all' 
    ? flags.every(flag => isEnabled(flag))
    : flags.some(flag => isEnabled(flag));
  
  return shouldShow ? <>{children}</> : <>{fallback}</>;
};

export default FeatureFlag;
