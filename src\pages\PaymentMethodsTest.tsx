import React, { useState } from 'react';
import { usePaymentMethods } from '../hooks/usePaymentMethods';
import { useUserLocation } from '../hooks/useUserLocation';
import { useFeatureFlags } from '../hooks/useFeatureFlags';
import PaymentMethodSelector from '../components/subscription/PaymentMethodSelector';
import { PaymentProcessor, PaymentMethodType } from '../types';
import Button from '../components/ui/button/Button';

/**
 * Simple test page to verify Chargily Pay integration
 */
export default function PaymentMethodsTest() {
  const [selectedProcessor, setSelectedProcessor] = useState<PaymentProcessor | ''>('');
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethodType | ''>('');

  const { data: paymentMethodsData, isLoading: methodsLoading, error: methodsError } = usePaymentMethods();
  const { location, isLoading: locationLoading, error: locationError } = useUserLocation();
  const { flags, override } = useFeatureFlags();

  const handlePaymentMethodChange = (processor: PaymentProcessor, method: PaymentMethodType) => {
    setSelectedProcessor(processor);
    setSelectedMethod(method);
  };

  const toggleChargilyFeature = () => {
    override('CHARGILY_PAYMENT', !flags.CHARGILY_PAYMENT);
  };

  const toggleMockAlgerianUser = () => {
    // This would require a page refresh to take effect
    const currentValue = localStorage.getItem('mockAlgerianUser') === 'true';
    localStorage.setItem('mockAlgerianUser', (!currentValue).toString());
    window.location.reload();
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Payment Methods Test
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Test the Chargily Pay integration and payment method selection
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column: Debug Info */}
          <div className="space-y-6">
            {/* Feature Flags */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Feature Flags
              </h2>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">Chargily Payment</span>
                  <div className="flex items-center space-x-2">
                    <span className={`text-xs font-medium px-2 py-1 rounded ${
                      flags.CHARGILY_PAYMENT 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                        : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                    }`}>
                      {flags.CHARGILY_PAYMENT ? 'ON' : 'OFF'}
                    </span>
                    <Button
                      onClick={toggleChargilyFeature}
                      variant="outline"
                      size="sm"
                    >
                      Toggle
                    </Button>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">Auto Selection</span>
                  <span className={`text-xs font-medium px-2 py-1 rounded ${
                    flags.CHARGILY_AUTO_SELECTION 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                      : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                  }`}>
                    {flags.CHARGILY_AUTO_SELECTION ? 'ON' : 'OFF'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">Location Detection</span>
                  <span className={`text-xs font-medium px-2 py-1 rounded ${
                    flags.AUTO_DETECT_LOCATION 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                      : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                  }`}>
                    {flags.AUTO_DETECT_LOCATION ? 'ON' : 'OFF'}
                  </span>
                </div>
              </div>
            </div>

            {/* Location Info */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Location Detection
              </h2>
              {locationLoading ? (
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
                </div>
              ) : locationError ? (
                <div className="text-red-600 dark:text-red-400">
                  Error: {locationError}
                </div>
              ) : location ? (
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Country:</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {location.country} ({location.countryCode})
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Is Algeria:</span>
                    <span className={`font-medium ${location.isAlgeria ? 'text-green-600' : 'text-gray-900 dark:text-white'}`}>
                      {location.isAlgeria ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Detection Method:</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {location.detectionMethod}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Confidence:</span>
                    <span className={`font-medium ${
                      location.confidence === 'high' ? 'text-green-600' :
                      location.confidence === 'medium' ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {location.confidence}
                    </span>
                  </div>
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400">Location not detected</p>
              )}
            </div>

            {/* Payment Methods Data */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Available Payment Methods
              </h2>
              {methodsLoading ? (
                <div className="animate-pulse space-y-2">
                  <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-full"></div>
                  <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
                </div>
              ) : methodsError ? (
                <div className="text-red-600 dark:text-red-400">
                  Error loading payment methods: {methodsError.message}
                </div>
              ) : paymentMethodsData?.data?.methods ? (
                <div className="space-y-3">
                  {paymentMethodsData.data.methods.map((method: any) => (
                    <div key={method.id} className="border border-gray-200 dark:border-gray-700 rounded p-3">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {method.displayName}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {method.description}
                          </p>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {method.supportedMethods.map((subMethod: string) => (
                              <span key={subMethod} className="text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                                {subMethod}
                              </span>
                            ))}
                          </div>
                        </div>
                        <div className="text-right">
                          <span className="text-sm font-medium text-gray-900 dark:text-white">
                            {method.currency}
                          </span>
                          {method.isRecommended && (
                            <div className="text-xs text-green-600 dark:text-green-400">
                              Recommended
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400">No payment methods available</p>
              )}
            </div>
          </div>

          {/* Right Column: Payment Method Selector */}
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Payment Method Selector
              </h2>
              
              <PaymentMethodSelector
                selectedProcessor={selectedProcessor}
                selectedMethod={selectedMethod}
                onSelectionChange={handlePaymentMethodChange}
                enableAutoSelection={true}
                showAutoSelectionInfo={true}
              />
              
              {/* Selection Summary */}
              {selectedProcessor && selectedMethod && (
                <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    Selected: {selectedProcessor} - {selectedMethod}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    This selection would be used for checkout
                  </p>
                </div>
              )}
            </div>

            {/* Expected Behavior */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Expected Behavior
              </h2>
              <div className="space-y-3 text-sm">
                <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
                  <p className="font-medium text-blue-900 dark:text-blue-300">For Algerian Users:</p>
                  <ul className="mt-1 text-blue-800 dark:text-blue-400 list-disc list-inside">
                    <li>Should see Chargily Pay with EDAHABIA & CIB options</li>
                    <li>Prices should be in DZD</li>
                    <li>Chargily should be auto-selected and recommended</li>
                    <li>LemonSqueezy should be available as fallback</li>
                  </ul>
                </div>
                <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded">
                  <p className="font-medium text-green-900 dark:text-green-300">For International Users:</p>
                  <ul className="mt-1 text-green-800 dark:text-green-400 list-disc list-inside">
                    <li>Should see LemonSqueezy with Card & PayPal options</li>
                    <li>Prices should be in USD</li>
                    <li>LemonSqueezy should be auto-selected and recommended</li>
                    <li>Chargily should NOT be visible</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
