import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { LocationService } from '../services/location.service';
import {
  LocationCreateRequest,
  LocationUpdateRequest,
  LocationFilters,
  Opening,
} from '../types';
import { ErrorLogger } from '../lib/error-utils';
import toast from 'react-hot-toast';

/**
 * Hook for fetching locations
 */
export const useLocations = (filters?: LocationFilters) => {
  return useQuery({
    queryKey: ['locations', filters],
    queryFn: () => LocationService.getLocations(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchLocations' });
    },
  });
};

/**
 * Hook for fetching a single location
 */
export const useLocation = (id: number) => {
  return useQuery({
    queryKey: ['locations', id],
    queryFn: () => LocationService.getLocation(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchLocation' });
    },
  });
};

/**
 * Hook for creating a location
 */
export const useCreateLocation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: LocationCreateRequest) => LocationService.createLocation(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['locations'] });
      queryClient.invalidateQueries({ queryKey: ['location-stats'] });
      toast.success('Location created successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to create location';
      ErrorLogger.log(error, { context: 'createLocation' });
      toast.error(message);
    },
  });
};

/**
 * Hook for updating a location
 */
export const useUpdateLocation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: LocationUpdateRequest }) =>
      LocationService.updateLocation(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['locations'] });
      queryClient.invalidateQueries({ queryKey: ['locations', id] });
      queryClient.invalidateQueries({ queryKey: ['location-stats'] });
      toast.success('Location updated successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to update location';
      ErrorLogger.log(error, { context: 'updateLocation' });
      toast.error(message);
    },
  });
};

/**
 * Hook for deleting a location
 */
export const useDeleteLocation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => LocationService.deleteLocation(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['locations'] });
      queryClient.invalidateQueries({ queryKey: ['location-stats'] });
      toast.success('Location deleted successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to delete location';
      ErrorLogger.log(error, { context: 'deleteLocation' });
      toast.error(message);
    },
  });
};

/**
 * Hook for fetching location statistics
 */
export const useLocationStats = () => {
  return useQuery({
    queryKey: ['location-stats'],
    queryFn: () => LocationService.getLocationStats(),
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchLocationStats' });
    },
  });
};

/**
 * Hook for fetching location operating hours
 */
export const useLocationHours = (locationId: number) => {
  return useQuery({
    queryKey: ['locations', locationId, 'hours'],
    queryFn: () => LocationService.getLocationHours(locationId),
    enabled: !!locationId,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchLocationHours' });
    },
  });
};

/**
 * Hook for updating location operating hours
 */
export const useUpdateLocationHours = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ locationId, hours }: { locationId: number; hours: Opening[] }) =>
      LocationService.updateLocationHours(locationId, hours),
    onSuccess: (_, { locationId }) => {
      queryClient.invalidateQueries({ queryKey: ['locations', locationId, 'hours'] });
      queryClient.invalidateQueries({ queryKey: ['locations', locationId] });
      toast.success('Operating hours updated successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to update operating hours';
      ErrorLogger.log(error, { context: 'updateLocationHours' });
      toast.error(message);
    },
  });
};
