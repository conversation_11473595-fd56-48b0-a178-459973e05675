# Provider Appointments Management System Documentation

## Overview

The Provider Appointments Management system in the Wasp.js application provides comprehensive functionality for healthcare providers to manage their appointments, including CRUD operations, status management, queue integration, and real-time updates.

## Table of Contents

1. [Database Schema](#database-schema)
2. [Provider Appointment CRUD Operations](#provider-appointment-crud-operations)
3. [API Endpoints](#api-endpoints)
4. [Authentication & Authorization](#authentication--authorization)
5. [Business Rules & Workflows](#business-rules--workflows)
6. [Queue Management Integration](#queue-management-integration)
7. [Credit System Integration](#credit-system-integration)
8. [Real-time Updates](#real-time-updates)
9. [Error Handling](#error-handling)
10. [File Upload Support](#file-upload-support)

## Database Schema

### Core Models

#### Appointment Model
```prisma
model Appointment {
  id                           Int                  @id @default(autoincrement())
  createdAt                    DateTime             @default(now())
  updatedAt                    DateTime             @updatedAt
  canceledAt                   DateTime?
  customerFolder               CustomerFolder       @relation(fields: [customerFolderId], references: [id])
  customerFolderId             Int
  typeEvent                    String               @default("clinic")
  status                       String               @default("pending") // pending, confirmed, canceled, completed, noshow, InProgress
  place                        SProvidingPlace      @relation(fields: [placeId], references: [id])
  placeId                      Int
  service                      Service              @relation(fields: [serviceId], references: [id])
  serviceId                    Int
  serviceDuration              Int
  expectedAppointmentStartTime DateTime?
  expectedAppointmentEndTime   DateTime?
  realAppointmentStartTime     DateTime?
  realAppointmentEndTime       DateTime?
  notes                        String?
  queue                        Queue?               @relation(fields: [queueId], references: [id])
  queueId                      Int?
  realTimeStatus               String               @default("ontime")
  slots                        Int                  @default(1)
  history                      AppointmentHistory[]
  isOverflowed                 Boolean?             @default(false)
  overflowReason               String?
  overflowProcessingStatus     String?
  overflowDetectedAt           DateTime?
  overflowProcessedAt          DateTime?
  rescheduleRequests           RescheduleRequest[]
}
```

#### AppointmentHistory Model
```prisma
model AppointmentHistory {
  id                Int         @id @default(autoincrement())
  appointment       Appointment @relation(fields: [appointmentId], references: [id])
  appointmentId     Int
  changedByUser     User        @relation(fields: [changedByUserId], references: [id])
  changedByUserId   String
  previousStartTime DateTime
  previousEndTime   DateTime
  newStartTime      DateTime
  newEndTime        DateTime
  previousStatus    String
  newStatus         String
  changeReason      String
  previousMotifId   Int         // previousServiceId
  newMotifId        Int         // newServiceId
  previousAgendaId  Int         // previousQueueId
  newAgendaId       Int         // newQueueId
  createdAt         DateTime    @default(now())
}
```

### Appointment Status Values
- `pending` - Initial status for new appointments
- `confirmed` - Provider has confirmed the appointment
- `InProgress` - Appointment is currently active
- `completed` - Appointment has been finished
- `canceled` - Appointment was cancelled
- `noshow` - Customer did not show up

## Provider Appointment CRUD Operations

### 1. Create Appointment

#### Wasp Operation
```typescript
// File: app/src/provider/operations.ts
export const createAppointment: CreateAppointment<CreateAppointmentData, Appointment>
```

#### Validation Schema
```typescript
const createAppointmentInputSchema = z.object({
  customerUserId: z.string().uuid("Invalid Customer User ID format"), 
  serviceId: z.number().int().positive("Invalid Service ID"),
  placeId: z.number().int().positive("Invalid Place ID"),
  queueId: z.number().int().positive("Queue ID is required"),
  startTime: z.date({ coerce: true }),
  endTime: z.date({ coerce: true }),
  notes: z.string().optional().nullable(),
}).refine(data => data.endTime > data.startTime, {
  message: "End time must be after start time",
  path: ["endTime"],
});
```

#### Business Logic
- **Credit Validation**: Provider must have at least 1 credit (PROVIDER_CREDIT_COST = 1)
- **Overlap Prevention**: Checks for conflicting appointments on the same queue
- **Customer Validation**: Verifies customer exists in provider's customer folder
- **Service Validation**: Ensures service belongs to the provider
- **Queue Validation**: Confirms queue belongs to the provider's place
- **Automatic History**: Creates appointment history record for audit trail

#### Transaction Flow
1. Validate provider credits (≥1 credit required)
2. Verify provider ownership of place/queue/service
3. Check customer folder exists for this provider
4. Validate no overlapping appointments on the queue
5. Decrement provider credits by 1
6. Create appointment record
7. Create appointment history entry
8. Trigger real-time queue update

### 2. Read/Retrieve Appointments

#### Wasp Query
```typescript
// File: app/src/provider/operations.ts
export const getAppointments: GetAppointments<void, Appointment[]>
```

#### Response Structure
```typescript
interface AppointmentResponse {
  id: number;
  status: string;
  expectedAppointmentStartTime?: Date;
  expectedAppointmentEndTime?: Date;
  realAppointmentStartTime?: Date;
  realAppointmentEndTime?: Date;
  notes?: string;
  service: {
    id: number;
    title: string;
    duration: number;
  };
  place: {
    id: number;
    name: string;
  };
  queue?: {
    id: number;
    title: string;
  };
  customerFolder: {
    userId: string;
    customer: {
      id: string;
      firstName: string;
      lastName: string;
    };
  };
}
```

#### Filtering Options
- **Date Range**: Filter by appointment date
- **Status**: Filter by appointment status
- **Queue**: Filter by specific queue
- **Customer**: Filter by customer
- **Service**: Filter by service type

#### Sorting & Pagination
- Default sort: `expectedAppointmentStartTime ASC`
- Pagination support via mobile API
- Real-time updates via WebSocket

### 3. Update Appointment

#### Wasp Operation
```typescript
// File: app/src/provider/operations.ts
export const updateAppointment: UpdateAppointment<UpdateAppointmentData, Appointment>
```

#### Validation Schema
```typescript
const updateAppointmentInputSchema = z.object({
  appointmentId: z.number().int().positive(),
  customerUserId: z.string().uuid("Invalid Customer User ID format"),
  serviceId: z.number().int().positive("Invalid Service ID"),
  placeId: z.number().int().positive("Invalid Place ID"),
  queueId: z.number().int().positive("Queue ID must be a positive integer").optional(),
  startTime: z.date({ coerce: true }),
  endTime: z.date({ coerce: true }),
  notes: z.string().optional().nullable(),
  status: z.string(), // Required for status transition logic
}).refine(data => data.endTime > data.startTime, {
  message: "End time must be after start time",
  path: ["endTime"],
});
```

#### Status Transition Logic

##### Moving to "InProgress"
- Sets `realAppointmentStartTime` to current timestamp
- Updates `expectedAppointmentStartTime` to current time
- Recalculates `expectedAppointmentEndTime` based on service duration
- Triggers queue position updates for subsequent appointments

##### Moving to "completed"
- Sets `realAppointmentEndTime` to current timestamp
- Must transition from "InProgress" status
- Triggers automatic queue shifting for remaining appointments
- Creates comprehensive appointment history

##### Cancellation Logic
- Updates status to "canceled"
- Sets `canceledAt` timestamp
- Handles credit implications based on cancellation timing
- Notifies affected customers

#### Editable Fields
- Service (with duration recalculation)
- Queue (with overlap validation)
- Start/End times (with conflict checking)
- Notes
- Status (with business rule validation)

### 4. Delete/Cancel Appointments

#### Cancellation Policies
- **Provider Cancellation**: No credit penalty for provider
- **Customer No-Show**: Customer loses credits, provider retains appointment credit
- **Early Cancellation**: Different rules based on timing
- **Emergency Cancellation**: Special handling for urgent situations

#### Notification Requirements
- Real-time WebSocket updates to affected queues
- Customer notifications via notification system
- Appointment history audit trail
- Queue position updates for subsequent appointments

## API Endpoints

### REST API Endpoints

#### Provider Appointment Management
```http
GET    /api/auth/providers/appointments              # Get all provider appointments
GET    /api/auth/providers/appointments/:id          # Get specific appointment
POST   /api/auth/providers/appointments              # Create new appointment
PUT    /api/auth/providers/appointments/:id          # Update appointment
PUT    /api/auth/providers/appointments/:id/status   # Update appointment status
```

#### Appointment Extensions
```http
PUT    /api/auth/providers/time/appointments/extend  # Extend appointment duration
```

#### Customer Booking (Provider Context)
```http
POST   /api/auth/appointments/customer-booking       # Create customer appointment
```

#### Provider Availability
```http
GET    /api/provider-availability                    # Get provider availability (public)
```

### Wasp Operations

#### Queries
```typescript
// Get all provider appointments
query getAppointments {
  fn: import { getAppointments } from "@src/provider/operations",
  entities: [Appointment, CustomerFolder, Service, SProvidingPlace, User, SProvider, Queue, Translation],
  auth: true
}

// Get provider availability
query getProviderAvailability {
  fn: import { getProviderAvailability } from "@src/provider/operations",
  entities: [SProvider, SProvidingPlace, Queue, Service, Opening, OpeningHours, Appointment, QueueOpening, QueueOpeningHours],
  auth: false
}
```

## Business Rules & Workflows

### Appointment Booking Flow (Provider Perspective)

#### 1. Pre-Booking Validation
- Provider must have sufficient credits (minimum 1 credit)
- Customer must exist in provider's customer folder
- Selected service must belong to the provider
- Queue must be active and belong to provider's location
- Time slot must not conflict with existing appointments

#### 2. Booking Process
```typescript
// Booking transaction flow
const bookingFlow = async (appointmentData) => {
  return await prisma.$transaction(async (tx) => {
    // 1. Validate provider credits
    const provider = await tx.user.findUnique({
      where: { id: providerUserId },
      select: { credits: true }
    });

    if (provider.credits < PROVIDER_CREDIT_COST) {
      throw new HttpError(402, 'Insufficient credits');
    }

    // 2. Check for overlapping appointments
    const conflicts = await tx.appointment.findMany({
      where: {
        queueId: appointmentData.queueId,
        status: { notIn: ['canceled', 'noshow'] },
        expectedAppointmentStartTime: { lt: appointmentData.endTime },
        expectedAppointmentEndTime: { gt: appointmentData.startTime },
      }
    });

    if (conflicts.length > 0) {
      throw new HttpError(409, 'Time slot conflict');
    }

    // 3. Deduct provider credits
    await tx.user.update({
      where: { id: providerUserId },
      data: { credits: { decrement: PROVIDER_CREDIT_COST } }
    });

    // 4. Create appointment
    const appointment = await tx.appointment.create({
      data: appointmentData
    });

    // 5. Create audit history
    await tx.appointmentHistory.create({
      data: {
        appointmentId: appointment.id,
        changedByUserId: providerUserId,
        changeReason: 'Initial appointment creation',
        // ... other history fields
      }
    });

    return appointment;
  });
};
```

#### 3. Post-Booking Actions
- Real-time queue state broadcast via WebSocket
- Customer notification (if enabled)
- Provider dashboard update
- Calendar integration sync

### Appointment Status Transitions

#### Valid Status Transitions
```
pending → confirmed → InProgress → completed
pending → canceled
confirmed → canceled
InProgress → completed
InProgress → canceled (emergency only)
any → noshow (provider decision)
```

#### Status Change Business Rules

##### Pending → Confirmed
- Provider explicitly accepts the appointment
- No additional validations required
- Customer receives confirmation notification

##### Confirmed → InProgress
- Automatically sets `realAppointmentStartTime`
- Recalculates expected end time based on actual start
- Triggers queue position updates for subsequent appointments
- Enables real-time tracking

##### InProgress → Completed
- Sets `realAppointmentEndTime`
- Calculates actual service duration
- Triggers automatic queue shifting
- Enables post-appointment actions (billing, feedback)

##### Any → Canceled
- Requires cancellation reason
- Handles credit implications:
  - Provider cancellation: No credit penalty
  - Customer no-show: Customer loses credits
  - Emergency cancellation: Special handling
- Sends notifications to affected parties
- Updates queue availability

##### Any → No-Show
- Provider marks customer as no-show
- Customer loses booking credits
- Provider retains appointment credit
- Appointment slot becomes available
- Automatic queue position updates

### Cancellation Policies

#### Provider-Initiated Cancellation
- **No Credit Penalty**: Provider retains credits
- **Notification Required**: Customer must be notified
- **Rescheduling Option**: Offer alternative time slots
- **History Tracking**: Full audit trail maintained

#### Customer No-Show Policy
- **Grace Period**: 15-minute default grace period
- **Credit Loss**: Customer loses booking credits
- **Provider Compensation**: Provider keeps appointment credit
- **Automatic Processing**: Queue automatically updates

#### Emergency Cancellation
- **Immediate Processing**: No waiting period
- **Special Reason Codes**: Medical emergency, facility issues, etc.
- **Credit Protection**: Both parties protected from credit loss
- **Priority Rescheduling**: Emergency appointments get priority rebooking

## Queue Management Integration

### Queue-Appointment Relationship

#### Queue Assignment
- Each appointment must be assigned to a specific queue
- Queue determines the physical location and service flow
- Multiple services can share the same queue
- Queue capacity affects appointment availability

#### Queue State Management
```typescript
interface QueueState {
  queueId: number;
  currentAppointments: Appointment[];
  nextAvailableSlot: Date;
  estimatedWaitTime: number;
  activeAppointment?: Appointment;
  upcomingAppointments: Appointment[];
}
```

#### Real-Time Queue Updates
- **WebSocket Integration**: Live updates via Socket.IO
- **Automatic Broadcasting**: Status changes trigger queue updates
- **Multi-Client Sync**: All connected clients receive updates
- **Conflict Resolution**: Optimistic locking for concurrent updates

### Availability Scheduling

#### Time Slot Calculation
```typescript
// Calculate available time slots
const calculateAvailableSlots = async (queueId: number, date: Date) => {
  // 1. Get queue operating hours
  const queueHours = await getQueueOperatingHours(queueId, date);

  // 2. Get existing appointments
  const existingAppointments = await getQueueAppointments(queueId, date);

  // 3. Calculate available slots
  const availableSlots = [];
  let currentTime = queueHours.startTime;

  while (currentTime < queueHours.endTime) {
    const slotEnd = new Date(currentTime.getTime() + serviceDuration * 60000);

    // Check for conflicts
    const hasConflict = existingAppointments.some(apt =>
      apt.expectedAppointmentStartTime < slotEnd &&
      apt.expectedAppointmentEndTime > currentTime
    );

    if (!hasConflict) {
      availableSlots.push({
        startTime: currentTime,
        endTime: slotEnd,
        available: true
      });
    }

    currentTime = new Date(currentTime.getTime() + slotInterval * 60000);
  }

  return availableSlots;
};
```

#### Service-Specific Configurations
- **Duration Mapping**: Each service has defined duration
- **Buffer Time**: Configurable time between appointments
- **Preparation Time**: Setup time for specific services
- **Cleanup Time**: Post-appointment cleanup duration

### Queue Position Management

#### Automatic Position Updates
- **Real-Time Calculation**: Position updates on status changes
- **Overflow Handling**: Manages appointments beyond normal hours
- **Priority Queuing**: Emergency appointments get priority
- **Wait Time Estimation**: Dynamic wait time calculations

#### Overflow Management
```typescript
interface OverflowAppointment {
  appointmentId: number;
  isOverflowed: boolean;
  overflowReason: string; // 'shifted_beyond_hours', 'provider_declared_early_closure'
  overflowProcessingStatus: string; // 'pending', 'confirmed_overtime', 'reschedule_requested'
  overflowDetectedAt: Date;
  overflowProcessedAt?: Date;
}
```

## Credit System Integration

### Credit Rules for Appointments

#### Provider Credit Costs
- **Appointment Creation**: 1 credit per appointment
- **No Refund Policy**: Credits not refunded on completion
- **Bulk Discounts**: Available for high-volume providers
- **Emergency Credits**: Special allocation for urgent situations

#### Customer Credit Requirements
- **Booking Requirement**: Customers need credits to book
- **Service-Specific Costs**: Different services may require different credit amounts
- **Cancellation Penalties**: Late cancellations result in credit loss
- **No-Show Penalties**: Full credit loss for no-shows

#### Credit Transaction Flow
```typescript
// Provider appointment creation
const createAppointmentWithCredits = async (appointmentData, context) => {
  return await prisma.$transaction(async (tx) => {
    // 1. Check provider credits
    const provider = await tx.user.findUnique({
      where: { id: context.user.id },
      select: { credits: true }
    });

    if (provider.credits < PROVIDER_CREDIT_COST) {
      throw new HttpError(402, 'Insufficient provider credits');
    }

    // 2. Check customer credits (if customer-initiated)
    if (appointmentData.customerInitiated) {
      const customer = await tx.user.findUnique({
        where: { id: appointmentData.customerUserId },
        select: { credits: true }
      });

      const requiredCredits = await calculateRequiredCredits(appointmentData.serviceId);
      if (customer.credits < requiredCredits) {
        throw new HttpError(402, 'Insufficient customer credits');
      }

      // Deduct customer credits
      await tx.user.update({
        where: { id: appointmentData.customerUserId },
        data: { credits: { decrement: requiredCredits } }
      });
    }

    // 3. Deduct provider credits
    await tx.user.update({
      where: { id: context.user.id },
      data: { credits: { decrement: PROVIDER_CREDIT_COST } }
    });

    // 4. Create appointment
    return await tx.appointment.create({
      data: appointmentData
    });
  });
};
```

#### Credit Recovery Scenarios
- **Provider Cancellation**: Customer credits refunded
- **Customer No-Show**: Provider credits protected
- **System Errors**: Automatic credit restoration
- **Dispute Resolution**: Manual credit adjustments

### Credit Monitoring & Alerts
- **Low Credit Warnings**: Alerts when credits below threshold
- **Usage Analytics**: Credit consumption patterns
- **Automatic Top-ups**: Integration with payment systems
- **Credit History**: Full transaction audit trail

## Real-time Updates

### WebSocket Integration

#### Socket.IO Implementation
```typescript
// WebSocket event handlers
interface ServerToClientEvents {
  queueStateUpdate: (data: QueueStateUpdate) => void;
  appointmentStatusChanged: (data: AppointmentStatusUpdate) => void;
  newAppointmentBooked: (data: NewAppointmentNotification) => void;
  appointmentCanceled: (data: AppointmentCancellation) => void;
}

interface ClientToServerEvents {
  requestQueueStatus: () => void;
  notifyQueueChange: (payload: { queueId: number }) => void;
}
```

#### Real-Time Event Broadcasting
```typescript
// Broadcast queue state updates
export const broadcastQueueStateUpdate = async (
  queueId: number,
  context: any,
  io: Server,
  triggeringSocket?: Socket
) => {
  try {
    // Get current queue state
    const queueState = await getCurrentQueueState(queueId, context);

    // Broadcast to all connected clients watching this queue
    io.emit('queueStateUpdate', {
      queueId,
      timestamp: new Date(),
      state: queueState,
      appointments: queueState.appointments,
      estimatedWaitTime: queueState.estimatedWaitTime
    });

    console.log(`Queue ${queueId} state broadcasted to all clients`);
  } catch (error) {
    console.error('Error broadcasting queue state:', error);
  }
};
```

#### Client-Side Integration
```typescript
// React component WebSocket integration
const useAppointmentUpdates = (queueId: number) => {
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [queueState, setQueueState] = useState<QueueState | null>(null);

  useEffect(() => {
    const socket = io();

    // Listen for queue updates
    socket.on('queueStateUpdate', (data) => {
      if (data.queueId === queueId) {
        setQueueState(data.state);
        setAppointments(data.appointments);
      }
    });

    // Listen for appointment status changes
    socket.on('appointmentStatusChanged', (data) => {
      setAppointments(prev =>
        prev.map(apt =>
          apt.id === data.appointmentId
            ? { ...apt, status: data.newStatus }
            : apt
        )
      );
    });

    // Request initial queue status
    socket.emit('requestQueueStatus');

    return () => socket.disconnect();
  }, [queueId]);

  return { appointments, queueState };
};
```

### Event Types & Payloads

#### Queue State Update
```typescript
interface QueueStateUpdate {
  queueId: number;
  timestamp: Date;
  state: {
    currentAppointment?: Appointment;
    upcomingAppointments: Appointment[];
    estimatedWaitTime: number;
    queueLength: number;
  };
}
```

#### Appointment Status Change
```typescript
interface AppointmentStatusUpdate {
  appointmentId: number;
  previousStatus: string;
  newStatus: string;
  timestamp: Date;
  changedBy: string;
  reason?: string;
}
```

#### New Appointment Notification
```typescript
interface NewAppointmentNotification {
  appointmentId: number;
  queueId: number;
  customerName: string;
  serviceName: string;
  expectedStartTime: Date;
  position: number;
}
```

## Error Handling

### HTTP Status Codes

#### Success Responses
- **200 OK**: Successful GET, PUT operations
- **201 Created**: Successful POST operations
- **204 No Content**: Successful DELETE operations

#### Client Error Responses
- **400 Bad Request**: Invalid request data, validation errors
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **409 Conflict**: Time slot conflicts, business rule violations
- **422 Unprocessable Entity**: Business logic validation failures

#### Server Error Responses
- **500 Internal Server Error**: Unexpected server errors
- **503 Service Unavailable**: System maintenance, overload

### Error Response Format
```typescript
interface ErrorResponse {
  success: false;
  message: string;
  code?: string;
  statusCode: number;
  details?: any;
  timestamp: Date;
  path: string;
}
```

### Common Error Scenarios

#### Validation Errors
```typescript
// Example validation error response
{
  "success": false,
  "message": "Validation failed",
  "statusCode": 400,
  "details": {
    "field": "startTime",
    "message": "End time must be after start time",
    "value": "2024-01-01T10:00:00Z"
  }
}
```

#### Business Rule Violations
```typescript
// Example business rule error
{
  "success": false,
  "message": "Insufficient credits to create appointment",
  "statusCode": 402,
  "code": "INSUFFICIENT_CREDITS",
  "details": {
    "required": 1,
    "available": 0,
    "userId": "user-123"
  }
}
```

#### Conflict Errors
```typescript
// Example time slot conflict
{
  "success": false,
  "message": "This time slot is already booked on the selected queue",
  "statusCode": 409,
  "code": "TIME_SLOT_CONFLICT",
  "details": {
    "queueId": 5,
    "conflictingAppointmentId": 123,
    "requestedStartTime": "2024-01-01T10:00:00Z",
    "requestedEndTime": "2024-01-01T11:00:00Z"
  }
}
```

### Error Handling Patterns

#### Try-Catch with HttpError
```typescript
export const createProviderAppointment = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Validate authentication
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Validate request data
    const appointmentData = validateAndExtract(createAppointmentSchema, req.body);

    // Call operation
    const newAppointment = await createAppointmentOp(appointmentData, context);

    return sendCreated(res, newAppointment, 'Appointment created successfully');

  } catch (error: any) {
    console.error('[createProviderAppointment] Error:', error);

    if (error instanceof HttpError) {
      return sendError(res, {
        statusCode: error.statusCode,
        message: error.message,
        code: error.code
      });
    }

    return sendError(res, error, 'Failed to create appointment');
  }
});
```

#### Validation Error Handling
```typescript
export function validateAndExtract<T extends z.ZodType>(
  schema: T,
  data: unknown
): z.infer<T> {
  const result = schema.safeParse(data);

  if (!result.success) {
    const errors = formatZodErrors(result.error);
    throw new HttpError(400, 'Validation failed', 'VALIDATION_ERROR', errors);
  }

  return result.data;
}
```

## File Upload Support

### Appointment-Related File Types
- **Medical Records**: PDF, images for medical appointments
- **Insurance Documents**: PDF files for insurance verification
- **Prescription Images**: JPEG, PNG for prescription uploads
- **Identification**: ID card images for verification

### File Upload API Integration
```typescript
// File upload for appointment documents
POST /api/auth/files/upload
Content-Type: application/json

{
  "fileType": "image/jpeg",
  "fileName": "prescription-scan.jpg",
  "appointmentId": 123,
  "documentType": "prescription"
}
```

### Supported File Types
```typescript
export const ALLOWED_FILE_TYPES = [
  'image/jpeg',
  'image/png',
  'application/pdf',
  'text/*',
  'video/quicktime',
  'video/mp4',
] as const;

export const MAX_FILE_SIZE_BYTES = 5 * 1024 * 1024; // 5MB
```

### File Upload Workflow
1. **Pre-signed URL Generation**: Client requests upload URL
2. **Direct S3 Upload**: Client uploads directly to S3
3. **Metadata Storage**: File metadata stored in database
4. **Appointment Association**: Link file to specific appointment
5. **Access Control**: Provider-only access to appointment files

### File Retrieval API
```typescript
// Get files for specific appointment
GET /api/auth/providers/appointments/:id/files

// Response format
{
  "success": true,
  "data": [
    {
      "id": "file-123",
      "fileName": "prescription-scan.jpg",
      "fileType": "image/jpeg",
      "uploadedAt": "2024-01-01T10:00:00Z",
      "downloadUrl": "https://s3.amazonaws.com/...",
      "documentType": "prescription"
    }
  ]
}
```

---

## API Reference Summary

### Complete Endpoint List

#### Provider Appointment Management
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/auth/providers/appointments` | Get all provider appointments | Yes |
| GET | `/api/auth/providers/appointments/:id` | Get specific appointment | Yes |
| POST | `/api/auth/providers/appointments` | Create new appointment | Yes |
| PUT | `/api/auth/providers/appointments/:id` | Update appointment | Yes |
| PUT | `/api/auth/providers/appointments/:id/status` | Update appointment status | Yes |
| PUT | `/api/auth/providers/time/appointments/extend` | Extend appointment duration | Yes |

#### Customer Booking (Provider Context)
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/auth/appointments/customer-booking` | Create customer appointment | Yes |

#### Provider Availability
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/provider-availability` | Get provider availability | No |

#### File Management
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/auth/files/upload` | Upload appointment file | Yes |
| GET | `/api/auth/providers/appointments/:id/files` | Get appointment files | Yes |

### Request/Response Examples

#### Create Appointment Request
```json
POST /api/auth/providers/appointments
{
  "customerId": "customer-uuid-123",
  "serviceId": 5,
  "queueId": 3,
  "expectedStartTime": "2024-01-15T10:00:00Z",
  "notes": "Follow-up appointment"
}
```

#### Create Appointment Response
```json
{
  "success": true,
  "data": {
    "id": 456,
    "status": "pending",
    "expectedAppointmentStartTime": "2024-01-15T10:00:00Z",
    "expectedAppointmentEndTime": "2024-01-15T10:30:00Z",
    "notes": "Follow-up appointment",
    "service": {
      "id": 5,
      "title": "General Consultation",
      "duration": 30
    },
    "place": {
      "id": 2,
      "name": "Main Clinic"
    },
    "queue": {
      "id": 3,
      "title": "General Queue"
    },
    "customer": {
      "id": "customer-uuid-123",
      "firstName": "John",
      "lastName": "Doe"
    }
  },
  "message": "Appointment created successfully"
}
```

#### Update Appointment Status Request
```json
PUT /api/auth/providers/appointments/456/status
{
  "status": "InProgress",
  "notes": "Patient arrived on time"
}
```

#### Get Provider Appointments Response
```json
{
  "success": true,
  "data": [
    {
      "id": 456,
      "status": "InProgress",
      "expectedAppointmentStartTime": "2024-01-15T10:00:00Z",
      "expectedAppointmentEndTime": "2024-01-15T10:30:00Z",
      "realAppointmentStartTime": "2024-01-15T10:02:00Z",
      "notes": "Patient arrived on time",
      "service": {
        "id": 5,
        "title": "General Consultation",
        "duration": 30
      },
      "place": {
        "id": 2,
        "name": "Main Clinic"
      },
      "queue": {
        "id": 3,
        "title": "General Queue"
      },
      "customer": {
        "id": "customer-uuid-123",
        "firstName": "John",
        "lastName": "Doe"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "totalPages": 3,
    "hasNext": true,
    "hasPrev": false
  }
}
```

---

## Implementation Notes

### Performance Considerations
- **Database Indexing**: Ensure proper indexes on frequently queried fields
- **Caching Strategy**: Implement Redis caching for frequently accessed data
- **Pagination**: Always implement pagination for list endpoints
- **Connection Pooling**: Optimize database connection management

### Security Best Practices
- **Input Validation**: Comprehensive validation using Zod schemas
- **SQL Injection Prevention**: Use Prisma ORM parameterized queries
- **Rate Limiting**: Implement rate limiting on API endpoints
- **Audit Logging**: Maintain comprehensive audit trails

### Monitoring & Observability
- **Error Tracking**: Implement error tracking and alerting
- **Performance Monitoring**: Monitor API response times and database queries
- **Business Metrics**: Track appointment creation rates, cancellation rates
- **Real-time Dashboards**: Provider and admin dashboards for monitoring

### Testing Strategy
- **Unit Tests**: Test individual operations and validation logic
- **Integration Tests**: Test complete API workflows
- **Load Testing**: Verify system performance under load
- **End-to-End Tests**: Test complete user journeys

This documentation provides a comprehensive overview of the Provider Appointments Management system, covering all aspects from database schema to API implementation, business rules, and technical considerations.

#### Actions
```typescript
// Create appointment
action createAppointment {
  fn: import { createAppointment } from "@src/provider/operations",
  entities: [Appointment, CustomerFolder, Service, SProvidingPlace, User, SProvider, Queue],
  auth: true
}

// Update appointment
action updateAppointment {
  fn: import { updateAppointment } from "@src/provider/operations",
  entities: [Appointment, AppointmentHistory, CustomerFolder, Service, SProvidingPlace, User, SProvider, Queue],
  auth: true
}

// Complete appointment
action completeAppointment {
  fn: import { completeAppointment } from "@src/provider/operations",
  entities: [Appointment, AppointmentHistory, User, CustomerFolder, Service, SProvider, Queue],
  auth: true
}

// Mark no-show
action noShowAppointment {
  fn: import { noShowAppointment } from "@src/provider/operations",
  entities: [Appointment, AppointmentHistory, User, CustomerFolder, Service, SProvider, Queue],
  auth: true
}
```

## Authentication & Authorization

### Authentication Requirements
- All provider appointment endpoints require `auth: true`
- JWT token validation via Wasp's built-in auth system
- User must have `role: 'CLIENT'` (provider role)

### Authorization Patterns
```typescript
// Provider authentication middleware
export function requireProviderAuth(req: Request, res: Response, next: Function, context: any) {
  if (!context.user) {
    return sendUnauthorized(res, 'Authentication required');
  }
  
  if (context.user.role !== 'CLIENT') {
    return sendForbidden(res, 'Provider access required');
  }
  
  next();
}
```

### Provider Ownership Validation
- Appointments can only be accessed by the owning provider
- Validation through `customerFolder.sProviderId` relationship
- Queue ownership verified through `queue.sProvidingPlaceId`
- Service ownership validated through provider's service list

### Context Structure
```typescript
interface ProviderApiContext {
  user: {
    id: string;
    role: string;
    credits: number;
  };
  entities: {
    Appointment: PrismaAppointmentDelegate;
    User: PrismaUserDelegate;
    SProvider: PrismaSProviderDelegate;
    // ... other entities
  };
}
```
