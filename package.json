{"name": "dalti-provider-dashboard", "private": true, "version": "2.0.2", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:run": "vitest run"}, "dependencies": {"@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@hookform/resolvers": "^5.1.1", "@react-jvectormap/core": "^1.0.4", "@react-jvectormap/world": "^1.1.2", "@tanstack/react-query": "^5.83.0", "apexcharts": "^4.1.0", "axios": "^1.10.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "flatpickr": "^4.6.13", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "react-loading-skeleton": "^3.5.0", "react-router": "^7.1.5", "socket.io-client": "^4.8.1", "swiper": "^11.2.3", "tailwind-merge": "^3.0.1", "zod": "^4.0.5"}, "devDependencies": {"@eslint/js": "^9.19.0", "@tailwindcss/postcss": "^4.0.8", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@types/socket.io-client": "^1.4.36", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "jsdom": "^26.1.0", "postcss": "^8.5.2", "tailwindcss": "^4.0.8", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0", "vite-plugin-svgr": "^4.3.0", "vitest": "^3.2.4"}, "overrides": {"react-helmet-async": {"react": "^16.8.0 || ^17 || ^18 || ^19"}, "@react-jvectormap/core": {"react": "^16.8.0 || ^17 || ^18 || ^19", "react-dom": "^16.8.0 || ^17 || ^18 || ^19"}, "@react-jvectormap/world": {"react": "^16.8.0 || ^17 || ^18 || ^19", "react-dom": "^16.8.0 || ^17 || ^18 || ^19"}}}