import React from 'react';
import { useUsedCredits } from '../../hooks/useUsedCredits';
import { useSubscriptionStatus } from '../../hooks/useSubscription';
import Button from '../ui/button/Button';

interface SubscriptionOverviewLayoutProps {
  className?: string;
}

export default function SubscriptionOverviewLayout({ className = '' }: SubscriptionOverviewLayoutProps) {
  const {
    used,
    remaining,
    allocated,
    totalAvailable,
    usagePercentage,
    currentPeriod,
    totalQueues,
    usedQueues,
    remainingQueues,
    isLoading: creditsLoading
  } = useUsedCredits();

  const { data: statusData, isLoading: statusLoading } = useSubscriptionStatus();

  const isLoading = creditsLoading || statusLoading;
  const subscription = statusData?.data?.subscription;
  const user = statusData?.data?.user;



  // Calculate queue usage from real API data
  const queueUsagePercentage = totalQueues > 0 ? Math.round((usedQueues / totalQueues) * 100) : 0;

  if (isLoading) {
    return (
      <div className={`grid grid-cols-1 lg:grid-cols-2 gap-6 ${className}`}>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700 animate-pulse">
          <div className="space-y-4">
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
            <div className="space-y-2">
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700 animate-pulse">
          <div className="space-y-4">
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
            <div className="space-y-3">
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`grid grid-cols-1 lg:grid-cols-1 gap-6 ${className}`}>

      {/* Right Column - Usage Statistics */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-12 border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Usage Statistics
          </h3>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {currentPeriod ? `${currentPeriod.month}/${currentPeriod.year}` : 'This Month'}
          </span>
        </div>

        {/* Credits Used */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
              </svg>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Credits Used</span>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {used || 0}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                of {totalAvailable || 0}
              </div>
            </div>
          </div>
          
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2">
            <div 
              className="h-2 rounded-full bg-blue-500"
              style={{ width: `${Math.min(usagePercentage || 0, 100)}%` }}
            ></div>
          </div>
          
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>{(usagePercentage || 0).toFixed(1)}% used</span>
            <span>{remaining || 0} remaining</span>
          </div>
        </div>

        {/* Queues Active */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Queues Active</span>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {usedQueues}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                of {totalQueues}
              </div>
            </div>
          </div>

          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2">
            <div
              className="h-2 rounded-full bg-purple-500"
              style={{ width: `${Math.min(queueUsagePercentage, 100)}%` }}
            ></div>
          </div>

          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>{queueUsagePercentage}% used</span>
            <span>{remainingQueues} available</span>
          </div>
        </div>

        {/* Current Plan */}
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400">Current Plan:</span>
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {subscription?.planId || 'Pro'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
