import { apiClient } from '../lib/api-client';
import { config } from '../lib/config';
import { UploadUrlResponse } from '../utils/s3-upload.utils';

/**
 * Profile picture response types
 */
export interface ProfilePictureResponse {
  hasProfilePicture: boolean;
  profilePicture?: {
    id: string;
    name: string;
    type: string;
    key: string;
    downloadUrl: string;
    createdAt: string;
  };
}

export interface ProfilePictureApiResponse {
  success: boolean;
  message?: string;
  data: ProfilePictureResponse;
}

export interface UploadUrlApiResponse {
  success: boolean;
  message: string;
  data: UploadUrlResponse;
}

export interface DeleteResponse {
  success: boolean;
  message: string;
}

/**
 * User service for profile picture management operations
 */
export class UserService {
  /**
   * Generate upload URL for profile picture
   * Phase 1 of the two-phase upload workflow
   */
  static async generateProfilePictureUploadUrl(
    fileName: string,
    fileType: string
  ): Promise<UploadUrlResponse> {
    const response = await apiClient.post<UploadUrlApiResponse>(
      config.endpoints.user.profilePicture,
      {
        fileName,
        fileType,
      }
    );

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to generate upload URL');
    }

    return response.data.data;
  }

  /**
   * Get current user's profile picture
   */
  static async getProfilePicture(): Promise<ProfilePictureResponse> {
    const response = await apiClient.get<ProfilePictureApiResponse>(
      config.endpoints.user.profilePicture
    );

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to fetch profile picture');
    }

    return response.data.data;
  }

  /**
   * Delete user's profile picture
   */
  static async deleteProfilePicture(): Promise<void> {
    const response = await apiClient.delete<DeleteResponse>(
      config.endpoints.user.profilePicture
    );

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to delete profile picture');
    }
  }

  /**
   * Check if user has a profile picture
   */
  static async hasProfilePicture(): Promise<boolean> {
    try {
      const profilePicture = await this.getProfilePicture();
      return profilePicture.hasProfilePicture;
    } catch (error) {
      // If there's an error fetching, assume no profile picture
      return false;
    }
  }

  /**
   * Get profile picture download URL
   */
  static async getProfilePictureUrl(): Promise<string | null> {
    try {
      const profilePicture = await this.getProfilePicture();
      return profilePicture.profilePicture?.downloadUrl || null;
    } catch (error) {
      return null;
    }
  }
}
