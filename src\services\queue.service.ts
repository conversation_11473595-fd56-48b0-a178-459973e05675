import { apiClient } from '../lib/api-client';
import { config } from '../lib/config';
import {
  Queue,
  QueueCreateRequest,
  QueueUpdateRequest,
  QueueFilters,
  QueueStats,
  QueueStatus,
  QueueSwapCreateRequest,
  QueueSwapResponse,
  QueueServiceCreateRequest,
  QueueLimits,
  QueueCapacity,
  QueueStateUpdate,
  QueueOpening
} from '../types/queue';

/**
 * Queue Management Service
 * Handles all queue-related API operations
 */
export class QueueService {
  /**
   * Get all queues for the provider
   */
  static async getQueues(filters?: QueueFilters): Promise<Queue[]> {
    const response = await apiClient.get<{
      success: boolean;
      data: Queue[];
      message: string;
    }>(
      config.endpoints.queues?.base || '/queues',
      { params: filters }
    );
    return response.data.data;
  }

  /**
   * Get a specific queue by ID
   */
  static async getQueue(id: number): Promise<Queue> {
    const response = await apiClient.get<{
      success: boolean;
      data: Queue;
      message: string;
    }>(`${config.endpoints.queues?.base || '/queues'}/${id}`);
    return response.data.data;
  }

  /**
   * Create a new queue
   */
  static async createQueue(data: QueueCreateRequest): Promise<Queue> {
    const response = await apiClient.post<{
      success: boolean;
      data: Queue;
      message: string;
    }>(
      config.endpoints.queues?.base || '/queues',
      data
    );
    return response.data.data;
  }

  /**
   * Update an existing queue
   */
  static async updateQueue(data: QueueUpdateRequest): Promise<Queue> {
    const response = await apiClient.put<{
      success: boolean;
      data: Queue;
      message: string;
    }>(
      `${config.endpoints.queues?.base || '/queues'}/${data.id}`,
      data
    );
    return response.data.data;
  }

  /**
   * Delete a queue
   */
  static async deleteQueue(id: number): Promise<void> {
    await apiClient.delete(`${config.endpoints.queues?.base || '/queues'}/${id}`);
  }

  /**
   * Get queue statistics
   */
  static async getQueueStats(): Promise<QueueStats> {
    const response = await apiClient.get<{
      success: boolean;
      data: QueueStats;
      message: string;
    }>(`${config.endpoints.queues?.base || '/queues'}/stats`);
    return response.data.data;
  }

  /**
   * Get real-time queue status
   */
  static async getQueueStatus(id: number): Promise<QueueStatus> {
    const response = await apiClient.get<{
      success: boolean;
      data: QueueStatus;
      message: string;
    }>(`${config.endpoints.queues?.base || '/queues'}/${id}/status`);
    return response.data.data;
  }

  /**
   * Add a service to a queue
   */
  static async addServiceToQueue(data: QueueServiceCreateRequest): Promise<void> {
    await apiClient.post(
      `${config.endpoints.queues?.base || '/queues'}/${data.queueId}/services`,
      data
    );
  }

  /**
   * Move customer to different position in queue
   */
  static async moveCustomerInQueue(
    queueId: number,
    customerId: string,
    newPosition: number
  ): Promise<void> {
    await apiClient.put(
      `${config.endpoints.queues?.base || '/queues'}/${queueId}/customers/${customerId}/position`,
      { position: newPosition }
    );
  }

  /**
   * Remove customer from queue
   */
  static async removeCustomerFromQueue(queueId: number, customerId: string): Promise<void> {
    await apiClient.delete(
      `${config.endpoints.queues?.base || '/queues'}/${queueId}/customers/${customerId}`
    );
  }

  /**
   * Create queue swap request
   */
  static async createSwapRequest(data: QueueSwapCreateRequest): Promise<void> {
    await apiClient.post(
      `${config.endpoints.queues?.base || '/queues'}/${data.queueId}/swap-requests`,
      data
    );
  }

  /**
   * Respond to queue swap request
   */
  static async respondToSwapRequest(
    requestId: number,
    response: QueueSwapResponse
  ): Promise<void> {
    await apiClient.put(
      `${config.endpoints.queues?.base || '/queues'}/swap-requests/${requestId}`,
      response
    );
  }

  /**
   * Activate/Deactivate queue
   */
  static async toggleQueueStatus(id: number, isActive: boolean): Promise<Queue> {
    const response = await apiClient.patch<{
      success: boolean;
      data: Queue;
      message: string;
    }>(
      `${config.endpoints.queues?.base || '/queues'}/${id}/status`,
      { isActive }
    );
    return response.data.data;
  }

  /**
   * Get queue analytics for a specific period
   */
  static async getQueueAnalytics(
    queueId: number,
    startDate: string,
    endDate: string
  ): Promise<any> {
    const response = await apiClient.get<{
      success: boolean;
      data: any;
      message: string;
    }>(
      `${config.endpoints.queues?.base || '/queues'}/${queueId}/analytics`,
      {
        params: { startDate, endDate }
      }
    );
    return response.data.data;
  }

  /**
   * Estimate wait time for new customer
   */
  static async estimateWaitTime(queueId: number, serviceId?: number): Promise<{
    estimatedWaitTime: number;
    position: number;
    customersAhead: number;
  }> {
    const response = await apiClient.get<{
      success: boolean;
      data: {
        estimatedWaitTime: number;
        position: number;
        customersAhead: number;
      };
      message: string;
    }>(
      `${config.endpoints.queues?.base || '/queues'}/${queueId}/estimate-wait`,
      { params: { serviceId } }
    );
    return response.data.data;
  }

  /**
   * Notify customers about queue updates
   */
  static async notifyCustomers(
    queueId: number,
    message: string,
    customerIds?: string[]
  ): Promise<void> {
    await apiClient.post(
      `${config.endpoints.queues?.base || '/queues'}/${queueId}/notify`,
      { message, customerIds }
    );
  }

  // ===== NEW API METHODS FROM DOCUMENTATION =====

  /**
   * Check if provider can create more queues
   */
  static async canCreateQueue(): Promise<QueueLimits> {
    const response = await apiClient.get<{
      success: boolean;
      data: QueueLimits;
      message: string;
    }>('/api/auth/providers/queues/can-create');
    return response.data.data;
  }

  /**
   * Get queue limits and usage
   */
  static async getQueueLimits(): Promise<QueueLimits> {
    const response = await apiClient.get<{
      success: boolean;
      data: QueueLimits;
      message: string;
    }>('/api/auth/providers/queues/limits');
    return response.data.data;
  }

  /**
   * Get queue services
   */
  static async getQueueServices(queueId: number): Promise<any[]> {
    const response = await apiClient.get<{
      success: boolean;
      data: any[];
      message: string;
    }>(`/api/auth/providers/queues/${queueId}/services`);
    return response.data.data;
  }

  /**
   * Assign service to queue
   */
  static async assignServiceToQueue(queueId: number, serviceId: number): Promise<Queue> {
    const response = await apiClient.post<{
      success: boolean;
      data: Queue;
      message: string;
    }>(`/api/auth/providers/queues/${queueId}/services`, { serviceId });
    return response.data.data;
  }

  /**
   * Remove service from queue
   */
  static async removeServiceFromQueue(queueId: number, serviceId: number): Promise<Queue> {
    const response = await apiClient.delete<{
      success: boolean;
      data: Queue;
      message: string;
    }>(`/api/auth/providers/queues/${queueId}/services/${serviceId}`);
    return response.data.data;
  }

  /**
   * Get queue operating hours
   */
  static async getQueueHours(queueId: number): Promise<QueueOpening[]> {
    const response = await apiClient.get<{
      success: boolean;
      data: QueueOpening[];
      message: string;
    }>(`/api/auth/providers/queues/${queueId}/hours`);
    return response.data.data;
  }

  /**
   * Update queue operating hours
   */
  static async updateQueueHours(queueId: number, hours: any[]): Promise<QueueOpening[]> {
    const response = await apiClient.put<{
      success: boolean;
      data: QueueOpening[];
      message: string;
    }>(`/api/auth/providers/queues/${queueId}/hours`, { hours });
    return response.data.data;
  }

  /**
   * Calculate queue capacity for a specific date
   */
  static async getQueueCapacity(queueId: number, date: string): Promise<QueueCapacity> {
    const response = await apiClient.get<{
      success: boolean;
      data: QueueCapacity;
      message: string;
    }>(`/api/auth/providers/queues/${queueId}/capacity`, { params: { date } });
    return response.data.data;
  }
}
