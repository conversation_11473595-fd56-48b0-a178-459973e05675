import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { WebSocketService } from '../websocket.service';
import { io } from 'socket.io-client';
import { TokenManager } from '../../lib/api-client';

// Mock socket.io-client
vi.mock('socket.io-client', () => ({
  io: vi.fn(),
}));

// Mock TokenManager
vi.mock('../../lib/api-client', () => ({
  TokenManager: {
    getToken: vi.fn(),
  },
}));

// Mock config
vi.mock('../../lib/config', () => ({
  config: {
    api: {
      baseUrl: 'http://localhost:3000',
    },
  },
}));

describe('WebSocketService', () => {
  let webSocketService: WebSocketService;
  let mockSocket: any;

  beforeEach(() => {
    vi.clearAllMocks();
    webSocketService = new WebSocketService();

    mockSocket = {
      connected: false,
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
      disconnect: vi.fn(),
      removeAllListeners: vi.fn(),
    };

    (io as Mock).mockReturnValue(mockSocket);
    (TokenManager.getToken as Mock).mockReturnValue('mock-token');
  });

  describe('connect', () => {
    it('should connect to WebSocket server successfully', async () => {
      const providerId = 'provider-123';
      
      // Simulate successful connection
      const connectPromise = webSocketService.connect(providerId);
      
      // Trigger connect event
      const connectHandler = mockSocket.on.mock.calls.find(
        (call: any[]) => call[0] === 'connect'
      )?.[1];
      connectHandler?.();

      await expect(connectPromise).resolves.toBeUndefined();

      expect(io).toHaveBeenCalledWith('http://localhost:3000', {
        auth: {
          token: 'mock-token',
          providerId,
        },
        transports: ['websocket', 'polling'],
        timeout: 10000,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
      });

      expect(mockSocket.emit).toHaveBeenCalledWith('joinProviderRoom', providerId);
    });

    it('should handle connection errors', async () => {
      const providerId = 'provider-123';
      
      const connectPromise = webSocketService.connect(providerId);
      
      // Trigger multiple connection errors to exceed max attempts
      const errorHandler = mockSocket.on.mock.calls.find(
        (call: any[]) => call[0] === 'connect_error'
      )?.[1];
      
      // Trigger 5 errors (max attempts)
      for (let i = 0; i < 5; i++) {
        errorHandler?.(new Error('Connection failed'));
      }

      await expect(connectPromise).rejects.toThrow('Failed to connect to WebSocket after maximum attempts');
    });

    it('should not connect if already connected', async () => {
      mockSocket.connected = true;
      
      const providerId = 'provider-123';
      await webSocketService.connect(providerId);

      expect(io).not.toHaveBeenCalled();
    });
  });

  describe('disconnect', () => {
    it('should disconnect from WebSocket server', () => {
      // Set up connected socket
      (webSocketService as any).socket = mockSocket;

      webSocketService.disconnect();

      expect(mockSocket.disconnect).toHaveBeenCalled();
      expect((webSocketService as any).socket).toBeNull();
    });

    it('should handle disconnect when no socket exists', () => {
      expect(() => webSocketService.disconnect()).not.toThrow();
    });
  });

  describe('isConnected', () => {
    it('should return true when socket is connected', () => {
      mockSocket.connected = true;
      (webSocketService as any).socket = mockSocket;

      expect(webSocketService.isConnected()).toBe(true);
    });

    it('should return false when socket is not connected', () => {
      mockSocket.connected = false;
      (webSocketService as any).socket = mockSocket;

      expect(webSocketService.isConnected()).toBe(false);
    });

    it('should return false when no socket exists', () => {
      expect(webSocketService.isConnected()).toBe(false);
    });
  });

  describe('Event Listeners', () => {
    beforeEach(() => {
      (webSocketService as any).socket = mockSocket;
    });

    it('should register queue state update listener', () => {
      const callback = vi.fn();
      webSocketService.onQueueStateUpdate(callback);

      expect(mockSocket.on).toHaveBeenCalledWith('queueStateUpdate', callback);
    });

    it('should register appointment status change listener', () => {
      const callback = vi.fn();
      webSocketService.onAppointmentStatusChanged(callback);

      expect(mockSocket.on).toHaveBeenCalledWith('appointmentStatusChanged', callback);
    });

    it('should register new appointment listener', () => {
      const callback = vi.fn();
      webSocketService.onNewAppointmentBooked(callback);

      expect(mockSocket.on).toHaveBeenCalledWith('newAppointmentBooked', callback);
    });

    it('should register appointment cancellation listener', () => {
      const callback = vi.fn();
      webSocketService.onAppointmentCanceled(callback);

      expect(mockSocket.on).toHaveBeenCalledWith('appointmentCanceled', callback);
    });

    it('should register appointment reschedule listener', () => {
      const callback = vi.fn();
      webSocketService.onAppointmentRescheduled(callback);

      expect(mockSocket.on).toHaveBeenCalledWith('appointmentRescheduled', callback);
    });

    it('should register provider credits update listener', () => {
      const callback = vi.fn();
      webSocketService.onProviderCreditsUpdated(callback);

      expect(mockSocket.on).toHaveBeenCalledWith('providerCreditsUpdated', callback);
    });
  });

  describe('Event Emission', () => {
    beforeEach(() => {
      (webSocketService as any).socket = mockSocket;
    });

    it('should request queue status', () => {
      const queueId = 123;
      webSocketService.requestQueueStatus(queueId);

      expect(mockSocket.emit).toHaveBeenCalledWith('requestQueueStatus', queueId);
    });

    it('should request queue status without queueId', () => {
      webSocketService.requestQueueStatus();

      expect(mockSocket.emit).toHaveBeenCalledWith('requestQueueStatus', undefined);
    });

    it('should notify queue change', () => {
      const queueId = 123;
      webSocketService.notifyQueueChange(queueId);

      expect(mockSocket.emit).toHaveBeenCalledWith('notifyQueueChange', { queueId });
    });
  });

  describe('Event Listener Management', () => {
    beforeEach(() => {
      (webSocketService as any).socket = mockSocket;
    });

    it('should remove all listeners', () => {
      webSocketService.removeAllListeners();

      expect(mockSocket.removeAllListeners).toHaveBeenCalled();
    });

    it('should remove specific event listener with callback', () => {
      const callback = vi.fn();
      webSocketService.off('queueStateUpdate', callback);

      expect(mockSocket.off).toHaveBeenCalledWith('queueStateUpdate', callback);
    });

    it('should remove specific event listener without callback', () => {
      webSocketService.off('queueStateUpdate');

      expect(mockSocket.off).toHaveBeenCalledWith('queueStateUpdate');
    });
  });

  describe('Error Handling', () => {
    it('should handle missing token gracefully', async () => {
      (TokenManager.getToken as Mock).mockReturnValue(null);
      
      const providerId = 'provider-123';
      
      await expect(webSocketService.connect(providerId)).rejects.toThrow();
    });

    it('should handle socket creation errors', async () => {
      (io as Mock).mockImplementation(() => {
        throw new Error('Socket creation failed');
      });

      const providerId = 'provider-123';
      
      await expect(webSocketService.connect(providerId)).rejects.toThrow('Socket creation failed');
    });
  });

  describe('Reconnection Logic', () => {
    it('should handle disconnect events', async () => {
      const providerId = 'provider-123';
      
      webSocketService.connect(providerId);
      
      const disconnectHandler = mockSocket.on.mock.calls.find(
        (call: any[]) => call[0] === 'disconnect'
      )?.[1];
      
      expect(() => disconnectHandler?.('transport close')).not.toThrow();
    });

    it('should reset connection state on disconnect', async () => {
      const providerId = 'provider-123';
      
      webSocketService.connect(providerId);
      
      const disconnectHandler = mockSocket.on.mock.calls.find(
        (call: any[]) => call[0] === 'disconnect'
      )?.[1];
      
      disconnectHandler?.('transport close');
      
      // Verify internal state is reset
      expect((webSocketService as any).isConnecting).toBe(false);
    });
  });

  // Integration tests for real-world scenarios
  describe('Integration Scenarios', () => {
    it('should handle complete appointment workflow', async () => {
      const providerId = 'provider-123';

      // Connect
      const connectPromise = webSocketService.connect(providerId);
      const connectHandler = mockSocket.on.mock.calls.find(
        (call: any[]) => call[0] === 'connect'
      )?.[1];
      connectHandler?.();
      await connectPromise;

      // Set up event listeners
      const queueUpdateCallback = vi.fn();
      const statusChangeCallback = vi.fn();
      const newAppointmentCallback = vi.fn();

      webSocketService.onQueueStateUpdate(queueUpdateCallback);
      webSocketService.onAppointmentStatusChanged(statusChangeCallback);
      webSocketService.onNewAppointmentBooked(newAppointmentCallback);

      // Simulate events
      const queueUpdateHandler = mockSocket.on.mock.calls.find(
        (call: any[]) => call[0] === 'queueStateUpdate'
      )?.[1];
      const statusChangeHandler = mockSocket.on.mock.calls.find(
        (call: any[]) => call[0] === 'appointmentStatusChanged'
      )?.[1];
      const newAppointmentHandler = mockSocket.on.mock.calls.find(
        (call: any[]) => call[0] === 'newAppointmentBooked'
      )?.[1];

      // Trigger events
      queueUpdateHandler?.({ queueId: 1, timestamp: new Date(), state: {} });
      statusChangeHandler?.({ appointmentId: 1, newStatus: 'confirmed', timestamp: new Date() });
      newAppointmentHandler?.({ appointmentId: 2, customerName: 'John Doe', serviceName: 'Consultation' });

      expect(queueUpdateCallback).toHaveBeenCalled();
      expect(statusChangeCallback).toHaveBeenCalled();
      expect(newAppointmentCallback).toHaveBeenCalled();

      // Clean up
      webSocketService.disconnect();
      expect(mockSocket.disconnect).toHaveBeenCalled();
    });
  });
});
