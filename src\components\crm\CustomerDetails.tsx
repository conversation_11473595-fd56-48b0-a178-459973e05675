import React from 'react';
import Button from '../ui/button/Button';
import { Customer } from '../../types/customer';

interface CustomerDetailsProps {
  customer: Customer;
  onClose: () => void;
  onEdit: () => void;
}

export default function CustomerDetails({ customer, onClose, onEdit }: CustomerDetailsProps) {
  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          Customer Details
        </h2>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div className="space-y-6">
        {/* Basic Information */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Basic Information
          </h3>
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Name:</span>
              <span className="text-sm text-gray-900 dark:text-white">
                {customer.firstName} {customer.lastName}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Email:</span>
              <span className="text-sm text-gray-900 dark:text-white">{customer.email}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Phone:</span>
              <span className="text-sm text-gray-900 dark:text-white">{customer.phone || 'Not provided'}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Member Since:</span>
              <span className="text-sm text-gray-900 dark:text-white">
                {new Date(customer.createdAt).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>

        {/* Customer Stats */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Customer Statistics
          </h3>
          <div className="grid grid-cols-3 gap-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {customer.folder?.totalVisits || 0}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Total Visits
              </div>
            </div>
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                ${customer.folder?.totalSpent || 0}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Total Spent
              </div>
            </div>
            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {customer.loyaltyPoints || 0}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Loyalty Points
              </div>
            </div>
          </div>
        </div>

        {/* Tags */}
        {customer.folder?.tags && customer.folder.tags.length > 0 && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Tags
            </h3>
            <div className="flex flex-wrap gap-2">
              {customer.folder.tags.map((tag, index) => (
                <span 
                  key={index}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Notes */}
        {customer.folder?.notes && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Notes
            </h3>
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <p className="text-sm text-gray-700 dark:text-gray-300">
                {customer.folder.notes}
              </p>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <Button
            variant="outline"
            onClick={onClose}
          >
            Close
          </Button>
          <Button onClick={onEdit}>
            Edit Customer
          </Button>
        </div>
      </div>
    </div>
  );
}
