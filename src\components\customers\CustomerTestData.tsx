import React from 'react';
import { ProviderCustomer } from '../../types/provider-customer';

/**
 * Mock data for testing the customer management functionality
 * This can be used when the backend API is not yet available
 */
export const mockCustomers: ProviderCustomer[] = [
  {
    id: '1',
    firstName: '<PERSON>',
    lastName: 'Doe',
    mobileNumber: '+213555123456',
    email: '<EMAIL>',
    nationalId: '1234567890123',
    notes: 'Regular customer, prefers morning appointments. Has a history of punctuality.',
    appointmentCount: 5,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T14:30:00Z',
    isActive: true,
  },
  {
    id: '2',
    firstName: 'Jane',
    lastName: 'Smith',
    mobileNumber: '+213555654321',
    email: '<EMAIL>',
    nationalId: undefined,
    notes: 'New customer, very friendly. Interested in premium services.',
    appointmentCount: 2,
    createdAt: '2024-01-20T14:30:00Z',
    isActive: true,
  },
  {
    id: '3',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    mobileNumber: '+213555789012',
    email: undefined,
    nationalId: '9876543210987',
    notes: undefined,
    appointmentCount: 8,
    createdAt: '2023-12-10T09:15:00Z',
    updatedAt: '2024-01-18T16:45:00Z',
    isActive: true,
  },
  {
    id: '4',
    firstName: 'Sarah',
    lastName: 'Johnson',
    mobileNumber: '+213555345678',
    email: '<EMAIL>',
    nationalId: undefined,
    notes: 'Prefers afternoon appointments. Has specific requirements for accessibility.',
    appointmentCount: 12,
    createdAt: '2023-11-05T11:20:00Z',
    isActive: true,
  },
  {
    id: '5',
    firstName: 'Mohamed',
    lastName: 'Benali',
    mobileNumber: '+213555901234',
    email: '<EMAIL>',
    nationalId: '5432167890123',
    notes: 'VIP customer. Always books premium services.',
    appointmentCount: 25,
    createdAt: '2023-10-01T08:00:00Z',
    updatedAt: '2024-01-22T10:15:00Z',
    isActive: true,
  },
  {
    id: '6',
    firstName: 'Lisa',
    lastName: 'Brown',
    mobileNumber: '+213555567890',
    email: '<EMAIL>',
    nationalId: undefined,
    notes: 'Inactive customer - moved to another city.',
    appointmentCount: 3,
    createdAt: '2023-09-15T13:30:00Z',
    isActive: false,
  },
];

/**
 * Mock API service for testing
 */
export class MockProviderCustomerService {
  static async getProviderCustomers(): Promise<ProviderCustomer[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockCustomers;
  }

  static async getProviderCustomer(id: string): Promise<ProviderCustomer> {
    await new Promise(resolve => setTimeout(resolve, 300));
    const customer = mockCustomers.find(c => c.id === id);
    if (!customer) {
      throw new Error('Customer not found');
    }
    return customer;
  }

  static async createProviderCustomer(data: any): Promise<ProviderCustomer> {
    await new Promise(resolve => setTimeout(resolve, 800));
    const newCustomer: ProviderCustomer = {
      id: Date.now().toString(),
      firstName: data.firstName,
      lastName: data.lastName,
      mobileNumber: data.mobileNumber,
      email: data.email,
      nationalId: data.nationalId,
      notes: data.notes,
      appointmentCount: 0,
      createdAt: new Date().toISOString(),
      isActive: true,
    };
    mockCustomers.unshift(newCustomer);
    return newCustomer;
  }

  static async updateProviderCustomer(data: any): Promise<ProviderCustomer> {
    await new Promise(resolve => setTimeout(resolve, 600));
    const index = mockCustomers.findIndex(c => c.id === data.customerUserId);
    if (index === -1) {
      throw new Error('Customer not found');
    }
    
    const updatedCustomer = {
      ...mockCustomers[index],
      ...data,
      updatedAt: new Date().toISOString(),
    };
    mockCustomers[index] = updatedCustomer;
    return updatedCustomer;
  }

  static async deactivateCustomerRelationship(id: string): Promise<{ success: boolean; message: string }> {
    await new Promise(resolve => setTimeout(resolve, 400));
    const index = mockCustomers.findIndex(c => c.id === id);
    if (index === -1) {
      throw new Error('Customer not found');
    }
    
    mockCustomers[index].isActive = false;
    return {
      success: true,
      message: 'Customer relationship deactivated successfully'
    };
  }
}

/**
 * Component for testing customer functionality with mock data
 */
export const CustomerTestDataProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <div>
      {children}
      <div className="fixed bottom-4 right-4 bg-yellow-100 border border-yellow-400 text-yellow-800 px-3 py-2 rounded-lg text-sm">
        <strong>Test Mode:</strong> Using mock data
      </div>
    </div>
  );
};
