import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { AppointmentService } from '../services/appointment.service';
import { ErrorLogger } from '../lib/error-utils';
import toast from 'react-hot-toast';

/**
 * Hook for fetching a customer
 */
export const useCustomer = (id: number) => {
  return useQuery({
    queryKey: ['customers', id],
    queryFn: () => AppointmentService.getCustomer(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchCustomer' });
    },
  });
};

/**
 * Hook for searching customers
 */
export const useSearchCustomers = (query: string) => {
  return useQuery({
    queryKey: ['customers', 'search', query],
    queryFn: () => AppointmentService.searchCustomers(query),
    enabled: query.length >= 2, // Only search when query is at least 2 characters
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'searchCustomers' });
    },
  });
};

/**
 * Hook for creating a customer
 */
export const useCreateCustomer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: {
      firstName: string;
      lastName: string;
      email: string;
      phone?: string;
    }) => AppointmentService.createCustomer(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
      toast.success('Customer created successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to create customer';
      ErrorLogger.log(error, { context: 'createCustomer' });
      toast.error(message);
    },
  });
};
