import React from 'react';
import Button from '../ui/button/Button';
import { Customer } from '../../types/customer';

interface CustomerCardProps {
  customer: Customer;
  viewMode: 'cards' | 'table';
  onView: () => void;
  onEdit: () => void;
}

export default function CustomerCard({ customer, viewMode, onView, onEdit }: CustomerCardProps) {
  const getMembershipColor = (level?: string) => {
    switch (level) {
      case 'platinum':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'gold':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'silver':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
      case 'bronze':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      default:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
    }
  };

  if (viewMode === 'table') {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 hover:shadow-md transition-shadow cursor-pointer"
           onClick={onView}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
              {customer.firstName?.charAt(0)}{customer.lastName?.charAt(0)}
            </div>
            <div>
              <h3 className="font-medium text-gray-900 dark:text-white">
                {customer.firstName} {customer.lastName}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {customer.email}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-center">
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                {customer.folder?.totalVisits || 0}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Visits
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                ${customer.folder?.totalSpent || 0}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Spent
              </div>
            </div>
            
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getMembershipColor(customer.membershipLevel)}`}>
              {customer.membershipLevel || 'Basic'}
            </span>
            
            <Button
              onClick={(e) => {
                e.stopPropagation();
                onEdit();
              }}
              variant="outline"
              size="sm"
              className="text-xs"
            >
              Edit
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow cursor-pointer"
         onClick={onView}>
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium">
            {customer.firstName?.charAt(0)}{customer.lastName?.charAt(0)}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {customer.firstName} {customer.lastName}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {customer.email}
            </p>
          </div>
        </div>
        
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getMembershipColor(customer.membershipLevel)}`}>
          {customer.membershipLevel || 'Basic'}
        </span>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-3 gap-4 mb-4">
        <div className="text-center">
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            {customer.folder?.totalVisits || 0}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Visits
          </div>
        </div>
        
        <div className="text-center">
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            ${customer.folder?.totalSpent || 0}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Spent
          </div>
        </div>
        
        <div className="text-center">
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            {customer.loyaltyPoints || 0}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Points
          </div>
        </div>
      </div>

      {/* Tags */}
      {customer.folder?.tags && customer.folder.tags.length > 0 && (
        <div className="mb-4">
          <div className="flex flex-wrap gap-1">
            {customer.folder.tags.slice(0, 3).map((tag, index) => (
              <span 
                key={index}
                className="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
              >
                {tag}
              </span>
            ))}
            {customer.folder.tags.length > 3 && (
              <span className="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400">
                +{customer.folder.tags.length - 3} more
              </span>
            )}
          </div>
        </div>
      )}

      {/* Last Visit */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="text-sm text-gray-500 dark:text-gray-400">
          Last visit: {customer.folder?.lastVisit 
            ? new Date(customer.folder.lastVisit).toLocaleDateString()
            : 'Never'
          }
        </div>
        
        <Button
          onClick={(e) => {
            e.stopPropagation();
            onEdit();
          }}
          variant="outline"
          size="sm"
          className="text-xs"
        >
          Edit
        </Button>
      </div>
    </div>
  );
}
