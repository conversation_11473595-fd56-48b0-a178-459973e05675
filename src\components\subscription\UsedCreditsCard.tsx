import React from 'react';
import { useUsedCredits } from '../../hooks/useUsedCredits';
import { ErrorDisplay } from '../error';

interface UsedCreditsCardProps {
  className?: string;
  showDetails?: boolean;
}

export const UsedCreditsCard: React.FC<UsedCreditsCardProps> = ({
  className = '',
  showDetails = true,
}) => {
  const {
    isLoading,
    error,
    used,
    remaining,
    allocated,
    totalAvailable,
    completed,
    usagePercentage,
    isNearLimit,
    isAtLimit,
    currentPeriod,
    refreshData,
  } = useUsedCredits();

  if (isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow p-6 ${className}`}>
        <ErrorDisplay
          error={error}
          title="Failed to load credits data"
          showRetry
          onRetry={refreshData}
          variant="inline"
          size="sm"
        />
      </div>
    );
  }

  const getProgressBarColor = () => {
    if (isAtLimit) return 'bg-red-500';
    if (isNearLimit) return 'bg-yellow-500';
    return 'bg-brand-500';
  };

  const getTextColor = () => {
    if (isAtLimit) return 'text-red-600 dark:text-red-400';
    if (isNearLimit) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-brand-600 dark:text-brand-400';
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Credits Usage
        </h3>
        {currentPeriod && (
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {currentPeriod.month}/{currentPeriod.year}
          </span>
        )}
      </div>

      <div className="space-y-4">
        {/* Usage Summary */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Credits Used
            </span>
            <span className={`text-sm font-semibold ${getTextColor()}`}>
              {used} / {totalAvailable}
            </span>
          </div>
          
          {/* Progress Bar */}
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${getProgressBarColor()}`}
              style={{ width: `${Math.min(usagePercentage, 100)}%` }}
            />
          </div>
          
          <div className="flex items-center justify-between mt-1">
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {usagePercentage}% used
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {remaining} remaining
            </span>
          </div>
        </div>

        {/* Details */}
        {showDetails && (
          <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Completed Appointments
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {completed}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Credits Remaining
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {remaining}
              </p>
            </div>
          </div>
        )}

        {/* Warning Messages */}
        {isAtLimit && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
            <p className="text-sm text-red-800 dark:text-red-200">
              ⚠️ You've reached your credit limit for this month. Consider upgrading your plan.
            </p>
          </div>
        )}
        
        {isNearLimit && !isAtLimit && (
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-3">
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
              ⚠️ You're approaching your credit limit. {remaining} credits remaining.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default UsedCreditsCard;
