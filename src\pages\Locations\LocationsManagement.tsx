import React, { useState } from 'react';
import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import PageMeta from "../../components/common/PageMeta";
import Button from "../../components/ui/button/Button";
import { Modal } from "../../components/ui/modal";
import { useModal } from "../../hooks/useModal";
import { ErrorDisplay } from "../../components/error";
import { useLocations, useDeleteLocation } from "../../hooks/useLocations";
import LocationForm from "../../components/locations/LocationForm";
import LocationCard from "../../components/locations/LocationCard";
import LocationHoursManager from "../../components/locations/LocationHoursManager";
import { Location, LocationFilters } from "../../types";

// Separate component for location results to prevent full page re-render
interface LocationResultsProps {
  filters: LocationFilters;
  onEditLocation: (location: Location) => void;
  onDeleteLocation: (id: number) => void;
  onManageHours: (location: Location) => void;
  onCreateLocation: () => void;
  deleteLocationMutation: any;
}

const LocationResults: React.FC<LocationResultsProps> = React.memo(({
  filters,
  onEditLocation,
  onDeleteLocation,
  onManageHours,
  onCreateLocation,
  deleteLocationMutation
}) => {
  const { data: locations, isLoading, error } = useLocations(filters);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <ErrorDisplay
          error={error}
          title="Failed to load locations"
          variant="card"
          showRetry
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <>
      {/* Locations Grid */}
      {locations && locations.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {locations.map((location) => (
            <LocationCard
              key={location.id}
              location={location}
              onEdit={() => onEditLocation(location)}
              onDelete={() => onDeleteLocation(location.id)}
              onManageHours={() => onManageHours(location)}
              isDeleting={deleteLocationMutation.isPending}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="mx-auto h-24 w-24 text-gray-400 mb-4">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No locations found
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-6">
            {Object.keys(filters).length > 0
              ? "No locations match your current filters. Try adjusting your search criteria."
              : "Get started by adding your first business location."
            }
          </p>
          {Object.keys(filters).length === 0 && (
            <Button onClick={onCreateLocation}>
              Add Your First Location
            </Button>
          )}
        </div>
      )}
    </>
  );
});

export default function LocationsManagement() {
  const [editingLocation, setEditingLocation] = useState<Location | null>(null);
  const [selectedLocationForHours, setSelectedLocationForHours] = useState<Location | null>(null);
  const [filters, setFilters] = useState<LocationFilters>({});
  const [modalType, setModalType] = useState<'location' | 'hours' | null>(null);
  const { isOpen, openModal, closeModal } = useModal();

  // Remove useLocations hook from here - it's now in LocationResults component
  const deleteLocationMutation = useDeleteLocation();

  const handleCreateLocation = () => {
    setEditingLocation(null);
    setModalType('location');
    openModal();
  };

  const handleEditLocation = (location: Location) => {
    setEditingLocation(location);
    setModalType('location');
    openModal();
  };

  const handleDeleteLocation = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this location? This action cannot be undone.')) {
      try {
        await deleteLocationMutation.mutateAsync(id);
      } catch (error) {
        // Error handled by mutation
      }
    }
  };

  const handleManageHours = (location: Location) => {
    setSelectedLocationForHours(location);
    setModalType('hours');
    openModal();
  };

  const handleCloseModal = () => {
    setEditingLocation(null);
    setSelectedLocationForHours(null);
    setModalType(null);
    closeModal();
  };

  const handleSuccess = () => {
    handleCloseModal();
  };

  const handleFilterChange = (newFilters: Partial<LocationFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  return (
    <>
      <PageMeta
        title="Locations Management | Provider Dashboard"
        description="Manage your business locations, addresses, and operating hours"
      />
      <PageBreadcrumb pageTitle="Locations" />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Locations Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Manage your business locations, addresses, and operating hours
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={handleCreateLocation}
              size="sm"
            >
              Add New Location
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                City
              </label>
              <input
                type="text"
                placeholder="Filter by city..."
                value={filters.city || ''}
                onChange={(e) => handleFilterChange({ city: e.target.value || undefined })}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 placeholder:text-gray-400 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Amenities
              </label>
              <select
                value={
                  filters.hasParking ? 'parking' : 
                  filters.hasElevator ? 'elevator' : 
                  filters.hasHandicapAccess ? 'handicap' : ''
                }
                onChange={(e) => {
                  const value = e.target.value;
                  handleFilterChange({
                    hasParking: value === 'parking' ? true : undefined,
                    hasElevator: value === 'elevator' ? true : undefined,
                    hasHandicapAccess: value === 'handicap' ? true : undefined,
                  });
                }}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="">All Locations</option>
                <option value="parking">With Parking</option>
                <option value="elevator">With Elevator</option>
                <option value="handicap">Handicap Accessible</option>
              </select>
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Search
              </label>
              <input
                type="text"
                placeholder="Search locations..."
                value={filters.search || ''}
                onChange={(e) => handleFilterChange({ search: e.target.value || undefined })}
                className="w-full h-10 rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 placeholder:text-gray-400 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-500"
              />
            </div>
          </div>
        </div>

        {/* Locations Grid - Now handled by LocationResults component */}
        <LocationResults
          filters={filters}
          onEditLocation={handleEditLocation}
          onDeleteLocation={handleDeleteLocation}
          onManageHours={handleManageHours}
          onCreateLocation={handleCreateLocation}
          deleteLocationMutation={deleteLocationMutation}
        />
      </div>

      {/* Location Modal */}
      <Modal
        isOpen={isOpen}
        onClose={handleCloseModal}
        className="max-w-[900px] p-0"
      >
        {modalType === 'location' ? (
          <LocationForm
            location={editingLocation}
            onClose={handleCloseModal}
            onSuccess={handleSuccess}
          />
        ) : modalType === 'hours' && selectedLocationForHours ? (
          <LocationHoursManager
            location={selectedLocationForHours}
            onClose={handleCloseModal}
          />
        ) : null}
      </Modal>
    </>
  );
}
