import React, { useState } from 'react';
import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import PageMeta from "../../components/common/PageMeta";
import Button from "../../components/ui/button/Button";
import { useModal } from "../../hooks/useModal";
import { ErrorDisplay } from "../../components/error";
import { 
  useSubscriptionStatus, 
  useUsageStatistics, 
  useSubscriptionPlans 
} from "../../hooks/useSubscription";
import {
  SubscriptionPlansGrid,
  SubscriptionStatusWidget,
  UsageStatistics,
  CheckoutModal,
  CustomerPortalCard,
  UsedCreditsCard,
  SubscriptionUsageOverview,
  SubscriptionOverviewLayout
} from "../../components/subscription";
import { useUsedCredits } from "../../hooks/useUsedCredits";

export default function SubscriptionManagement() {
  const [activeTab, setActiveTab] = useState<'overview' | 'plans' | 'usage'>('overview');
  const [selectedPlanId, setSelectedPlanId] = useState<string>('');
  const { isOpen: isCheckoutOpen, openModal: openCheckout, closeModal: closeCheckout } = useModal();

  const { data: statusData, isLoading: statusLoading, error: statusError } = useSubscriptionStatus();
  const { data: usageData, isLoading: usageLoading } = useUsageStatistics();
  const { data: plansData, isLoading: plansLoading } = useSubscriptionPlans();
  const {
    used,
    remaining,
    allocated,
    totalAvailable,
    totalQueues,
    usedQueues,
    completed,
    usagePercentage,
    isNearLimit,
    isAtLimit,
    currentPeriod,
    isLoading: creditsLoading,
    error: creditsError,
    refreshData: refreshCreditsData
  } = useUsedCredits();

  const handleUpgrade = () => {
    setSelectedPlanId(''); // Clear any preselected plan
    openCheckout();
  };

  const handlePlanSelect = (planId: string) => {
    console.log('💳 Plan selected for checkout:', planId);
    setSelectedPlanId(planId);
    openCheckout();
  };

  const handleCloseCheckout = () => {
    setSelectedPlanId(''); // Clear selected plan when modal closes
    closeCheckout();
  };

  const getCurrentPlan = () => {
    if (!statusData?.data?.subscription || !plansData?.data?.plans) return null;
    return plansData.data.plans.find(plan => plan.id === statusData.data.subscription.planId);
  };

  const isLoading = statusLoading || usageLoading || plansLoading;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-500"></div>
      </div>
    );
  }

  if (statusError) {
    return (
      <div className="p-6">
        <ErrorDisplay
          error={statusError}
          title="Failed to load subscription data"
          variant="card"
          showRetry
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  const currentPlan = getCurrentPlan();
  const subscription = statusData?.data?.subscription;
  const usage = usageData?.data;

  return (
    <>
      <PageMeta
        title="Subscription Management | Provider Dashboard"
        description="Manage your subscription, billing, and usage"
      />
      <PageBreadcrumb pageTitle="Subscription" />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Subscription Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Manage your subscription, view usage, and upgrade your plan
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-3">
            {subscription?.planId === 'free' ? (
              <Button onClick={handleUpgrade} size="sm">
                Upgrade Plan
              </Button>
            ) : (
              <Button onClick={handleUpgrade} variant="outline" size="sm">
                Change Plan
              </Button>
            )}

            {/* Debug button to manually fetch used credits */}
            <Button
              onClick={refreshCreditsData}
              variant="outline"
              size="sm"
              disabled={creditsLoading}
            >
              {creditsLoading ? 'Loading...' : '🔄 Refresh Usage'}
            </Button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'overview', label: 'Overview', icon: '📊' },
              { id: 'plans', label: 'Plans', icon: '💳' },
              { id: 'usage', label: 'Usage', icon: '📈' },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-brand-500 text-brand-600 dark:text-brand-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Overview Layout - Matches the design */}
              <SubscriptionOverviewLayout />


              {/* Current Plan Details */}
              {currentPlan && (
                <div className="lg:col-span-3">
                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      Current Plan Details
                    </h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <div>
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Plan Name</p>
                        <p className="text-lg font-semibold text-gray-900 dark:text-white">
                          {currentPlan.name}
                        </p>
                      </div>
                      
                      <div>
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Price</p>
                        <p className="text-lg font-semibold text-gray-900 dark:text-white">
                          {currentPlan.price}
                          {currentPlan.isSubscription && currentPlan.id !== 'free' && (
                            <span className="text-sm text-gray-500 dark:text-gray-400 ml-1">/month</span>
                          )}
                        </p>
                      </div>
                      
                      <div>
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Credits</p>
                        <p className="text-lg font-semibold text-gray-900 dark:text-white">
                          {currentPlan.effect.kind === 'credits' 
                            ? `${currentPlan.effect.amount} Credits`
                            : `${currentPlan.effect.amount}/month`
                          }
                        </p>
                      </div>
                      
                      <div>
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Queues</p>
                        <p className="text-lg font-semibold text-gray-900 dark:text-white">
                          {currentPlan.effect.queues || 'Unlimited'}
                        </p>
                      </div>
                    </div>

                    <div className="mt-4">
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Features</p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {currentPlan.features.map((feature, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                            <span className="text-sm text-gray-600 dark:text-gray-400">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Customer Portal Access */}
              <div className="lg:col-span-3">
                <CustomerPortalCard />
              </div>
            </div>
          )}

          {activeTab === 'plans' && (
            <div>
              <SubscriptionPlansGrid
                showRecommended={true}
                onPlanSelect={handlePlanSelect}
              />
            </div>
          )}

          {activeTab === 'usage' && (
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
              {/* Detailed Usage Overview */}
              <div className="xl:col-span-2">
                <SubscriptionUsageOverview />
              </div>

              {/* Historical Usage Statistics */}
              <div className="xl:col-span-2">
                <UsageStatistics showPeriodSelector={true} />
              </div>

              {/* Usage Insights - Using Real Data */}
              <div className="xl:col-span-2">
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Usage Insights
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {used}
                      </p>
                      <p className="text-sm text-blue-600 dark:text-blue-400">Credits Used</p>
                    </div>

                    <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {remaining}
                      </p>
                      <p className="text-sm text-green-600 dark:text-green-400">Credits Remaining</p>
                    </div>

                    <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                        {completed}
                      </p>
                      <p className="text-sm text-purple-600 dark:text-purple-400">Appointments Completed</p>
                    </div>

                    <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                      <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                        {usagePercentage.toFixed(1)}%
                      </p>
                      <p className="text-sm text-orange-600 dark:text-orange-400">Usage Rate</p>
                    </div>
                  </div>

                  {/* Usage Trend */}
                  {currentPeriod && (
                    <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                        Current Billing Period
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {currentPeriod.startDate.toLocaleDateString()} - {currentPeriod.endDate.toLocaleDateString()}
                      </p>
                      <div className="mt-2 flex items-center">
                        <div className="flex-1 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${
                              isAtLimit ? 'bg-red-500' :
                              isNearLimit ? 'bg-yellow-500' :
                              'bg-green-500'
                            }`}
                            style={{ width: `${Math.min(usagePercentage, 100)}%` }}
                          ></div>
                        </div>
                        <span className="ml-3 text-sm text-gray-600 dark:text-gray-400">
                          {usagePercentage.toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Checkout Modal */}
      <CheckoutModal
        isOpen={isCheckoutOpen}
        onClose={handleCloseCheckout}
        preselectedPlanId={selectedPlanId}
        title={selectedPlanId ? "Confirm Your Selection" : "Choose Your Plan"}
        showPlanComparison={!selectedPlanId}
      />
    </>
  );
}
