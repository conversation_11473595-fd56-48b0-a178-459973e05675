/**
 * Profile Page Completion Alert
 * Specialized completion alert for the profile page focusing on profile-related sections
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router';
import clsx from 'clsx';
import { useProfileCompletion } from '../../hooks/useProfileCompletion';
import { useAuth } from '../../context/AuthContext';
import { ProfileCompletionService } from '../../services/profile-completion.service';

interface ProfilePageCompletionAlertProps {
  className?: string;
  onSectionFocus?: (section: string) => void;
}

const ProfilePageCompletionAlert: React.FC<ProfilePageCompletionAlertProps> = ({
  className,
  onSectionFocus,
}) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { completion, isLoading } = useProfileCompletion();
  const [isExpanded, setIsExpanded] = useState(false);

  if (isLoading || !completion || !user) {
    return null;
  }

  // Only show if profile-related sections are incomplete
  const profilePictureIncomplete = !completion.breakdown.profilePicture.completed;
  const providerInfoIncomplete = !completion.breakdown.providerInfo.completed;
  
  if (!profilePictureIncomplete && !providerInfoIncomplete) {
    return null;
  }

  const profileRelatedPercentage = Math.round(
    (completion.breakdown.profilePicture.percentage * 10 + 
     completion.breakdown.providerInfo.percentage * 30) / 40
  );

  const handleSectionClick = (section: string) => {
    if (onSectionFocus) {
      onSectionFocus(section);
    }
    
    // Scroll to the relevant section on the page
    const sectionMap: Record<string, string> = {
      profilePicture: 'logo-section',
      providerInfo: 'basic-info-section',
    };
    
    const elementId = sectionMap[section];
    if (elementId) {
      const element = document.getElementById(elementId);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }
  };

  const handleNavigateToOtherSections = () => {
    // Navigate to locations if that's the next incomplete section
    if (!completion.breakdown.providingPlaces.completed) {
      navigate('/locations');
    } else if (!completion.breakdown.services.completed) {
      navigate('/services');
    } else if (!completion.breakdown.queues.completed) {
      navigate('/queues');
    }
  };



  const profileSteps = [];
  if (profilePictureIncomplete) {
    profileSteps.push({
      section: 'profilePicture',
      title: 'Upload Business Logo',
      description: 'Add a professional logo to build trust with customers',
      icon: '🖼️',
      action: 'Upload Logo',
    });
  }
  
  if (providerInfoIncomplete) {
    const missingFields = Object.entries(completion.breakdown.providerInfo.requiredFields || {})
      .filter(([_, isComplete]) => !isComplete)
      .map(([field, _]) => field);
    
    profileSteps.push({
      section: 'providerInfo',
      title: 'Complete Business Information',
      description: `Missing: ${missingFields.join(', ')}`,
      icon: '📋',
      action: 'Complete Info',
    });
  }

  return (
    <div className={clsx(
      'rounded-2xl border bg-white shadow-theme-sm dark:bg-white/[0.03] dark:border-gray-800',
      profileRelatedPercentage >= 80 ? 'border-success-200 bg-success-50 dark:bg-success-500/15 dark:border-success-500/30' :
      profileRelatedPercentage >= 50 ? 'border-brand-200 bg-brand-50 dark:bg-brand-500/15 dark:border-brand-500/30' :
      'border-warning-200 bg-warning-50 dark:bg-warning-500/15 dark:border-warning-500/30',
      className
    )}>
      <div className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className={clsx(
              'w-8 h-8 rounded-full flex items-center justify-center text-sm',
              profileRelatedPercentage >= 80 ? 'bg-success-100 text-success-600 dark:bg-success-500/15 dark:text-success-500' :
              profileRelatedPercentage >= 50 ? 'bg-brand-100 text-brand-600 dark:bg-brand-500/15 dark:text-brand-400' :
              'bg-warning-100 text-warning-600 dark:bg-warning-500/15 dark:text-orange-400'
            )}>
              📊
            </div>
            <div>
              <h3 className={clsx(
                'text-sm font-medium',
                profileRelatedPercentage >= 80 ? 'text-success-800 dark:text-success-500' :
                profileRelatedPercentage >= 50 ? 'text-brand-800 dark:text-brand-400' :
                'text-warning-800 dark:text-orange-400'
              )}>
                Complete Your Profile Setup
              </h3>
              <p className={clsx(
                'text-xs',
                profileRelatedPercentage >= 80 ? 'text-success-700 dark:text-success-500' :
                profileRelatedPercentage >= 50 ? 'text-brand-700 dark:text-brand-400' :
                'text-warning-700 dark:text-orange-400'
              )}>
                Profile sections: {profileRelatedPercentage}% complete
              </p>
            </div>
          </div>
          
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className={clsx(
              'text-xs font-medium transition-colors',
              profileRelatedPercentage >= 80 ? 'text-green-600 hover:text-green-700 dark:text-green-400' :
              profileRelatedPercentage >= 50 ? 'text-blue-600 hover:text-blue-700 dark:text-blue-400' :
              'text-amber-600 hover:text-amber-700 dark:text-amber-400'
            )}
          >
            {isExpanded ? 'Show Less' : 'Show Details'}
          </button>
        </div>

        {/* Progress Bar */}
        <div className="mb-3">
          <div className="flex justify-between items-center mb-1">
            <span className={clsx(
              'text-xs font-medium',
              profileRelatedPercentage >= 80 ? 'text-success-700 dark:text-success-500' :
              profileRelatedPercentage >= 50 ? 'text-brand-700 dark:text-brand-400' :
              'text-warning-700 dark:text-orange-400'
            )}>
              Profile Progress
            </span>
            <span className={clsx(
              'text-xs font-semibold',
              profileRelatedPercentage >= 80 ? 'text-success-800 dark:text-success-500' :
              profileRelatedPercentage >= 50 ? 'text-brand-800 dark:text-brand-400' :
              'text-warning-800 dark:text-orange-400'
            )}>
              {profileRelatedPercentage}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
            <div
              className={clsx(
                'h-2 rounded-full transition-all duration-300',
                profileRelatedPercentage >= 80 ? 'bg-success-500' :
                profileRelatedPercentage >= 50 ? 'bg-brand-500' :
                'bg-warning-500'
              )}
              style={{ width: `${profileRelatedPercentage}%` }}
            />
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex flex-wrap gap-2 mb-3">
          {profileSteps.map((step) => (
            <button
              key={step.section}
              onClick={() => handleSectionClick(step.section)}
              className={clsx(
                'inline-flex items-center px-3 py-1 rounded-full text-xs font-medium transition-colors',
                profileRelatedPercentage >= 80 ? 'bg-success-100 text-success-700 hover:bg-success-200 dark:bg-success-500/15 dark:text-success-500 dark:hover:bg-success-500/25' :
                profileRelatedPercentage >= 50 ? 'bg-brand-100 text-brand-700 hover:bg-brand-200 dark:bg-brand-500/15 dark:text-brand-400 dark:hover:bg-brand-500/25' :
                'bg-warning-100 text-warning-700 hover:bg-warning-200 dark:bg-warning-500/15 dark:text-orange-400 dark:hover:bg-warning-500/25'
              )}
            >
              <span className="mr-1">{step.icon}</span>
              {step.action}
            </button>
          ))}
        </div>

        {/* Expanded Details */}
        {isExpanded && (
          <div className="border-t border-gray-200 dark:border-gray-600 pt-3 space-y-2">
            {profileSteps.map((step) => (
              <div
                key={step.section}
                className="flex items-center justify-between p-2 bg-white dark:bg-gray-700 rounded-md border border-gray-100 dark:border-gray-600"
              >
                <div className="flex items-center space-x-2">
                  <span className="text-lg">{step.icon}</span>
                  <div>
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {step.title}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      {step.description}
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => handleSectionClick(step.section)}
                  className="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors"
                >
                  {step.action}
                </button>
              </div>
            ))}
            
            {/* Overall Progress Link */}
            <div className="pt-2 border-t border-gray-100 dark:border-gray-600">
              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-600 dark:text-gray-400">
                  Overall setup: {completion.overallPercentage}% complete
                </span>
                <button
                  onClick={handleNavigateToOtherSections}
                  className="text-blue-600 hover:text-blue-700 dark:text-blue-400 font-medium"
                >
                  Continue Setup →
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Success Message */}
        {profileRelatedPercentage >= 100 && (
          <div className="flex items-center text-xs text-success-700 dark:text-success-500">
            <span className="mr-1">✅</span>
            Profile sections complete!
            {completion.overallPercentage < 100 && (
              <button
                onClick={handleNavigateToOtherSections}
                className="ml-1 text-brand-600 hover:text-brand-700 dark:text-brand-400 dark:hover:text-brand-300 font-medium"
              >
                Continue with other sections →
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProfilePageCompletionAlert;
