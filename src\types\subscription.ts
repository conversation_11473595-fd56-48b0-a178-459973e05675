/**
 * Subscription and Payment related types
 * Based on LemonSqueezy Payment Subscription API
 */

/**
 * Subscription Plan Effect Types
 */
export interface SubscriptionPlanEffect {
  type: 'recurrent' | 'one-time';
  kind: 'subscription' | 'credits';
  amount: number; // Credits amount
  queues: number | null; // Queue limit (null for credit-only plans)
}

/**
 * Subscription Plan
 */
export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: string; // Display price (e.g., "€9.99", "Free")
  features: string[];
  effect: SubscriptionPlanEffect;
  isSubscription: boolean;
  isOneTime: boolean;
}

/**
 * Subscription Plans Response
 */
export interface SubscriptionPlansResponse {
  success: boolean;
  message: string;
  data: {
    plans: SubscriptionPlan[];
  };
}

/**
 * User Subscription Details
 */
export interface UserSubscription {
  isActive: boolean;
  status: SubscriptionStatus;
  planId: string;
  planName: string;
  planDetails: {
    effect: SubscriptionPlanEffect;
    features: string[];
  };
}

/**
 * User Payment Status
 */
export interface UserPaymentStatus {
  user: {
    id: string;
    email: string;
    credits: number;
    queues: number;
    datePaid?: string;
  };
  subscription: UserSubscription;
  hasCustomerPortal: boolean;
}

/**
 * User Payment Status Response
 */
export interface UserPaymentStatusResponse {
  success: boolean;
  message: string;
  data: UserPaymentStatus;
}

/**
 * Subscription Status Types
 */
export type SubscriptionStatus = 
  | 'active'
  | 'past_due'
  | 'cancel_at_period_end'
  | 'deleted'
  | 'inactive';

/**
 * Checkout Session Request (backward compatible)
 */
export interface CheckoutSessionRequest {
  planId: string;
  paymentProcessor?: PaymentProcessor;
  paymentMethod?: PaymentMethodType;
  billingCycle?: 'monthly' | 'yearly';
  metadata?: Record<string, any>;
}

/**
 * Checkout Session Response
 */
export interface CheckoutSessionResponse {
  success: boolean;
  message: string;
  data: {
    sessionUrl: string;
    sessionId: string;
    planId: string;
    planName: string;
  };
}

/**
 * Customer Portal Response
 */
export interface CustomerPortalResponse {
  success: boolean;
  message: string;
  data?: {
    customerPortalUrl: string;
  };
}

/**
 * Usage Statistics Period
 */
export type UsagePeriod = 'week' | 'month' | 'year';

/**
 * Current Usage Limits
 */
export interface UsageLimits {
  credits: number;
  queues: number;
}

/**
 * Current Usage Data
 */
export interface CurrentUsage {
  credits: number;
  queues: number;
}

/**
 * Usage Statistics
 */
export interface UsageStatistics {
  period: UsagePeriod;
  current: CurrentUsage;
  limits: UsageLimits;
  subscription: {
    planId: string;
    planName: string;
    status: SubscriptionStatus;
    isActive: boolean;
  };
}

/**
 * Usage Statistics Response
 */
export interface UsageStatisticsResponse {
  success: boolean;
  message: string;
  data: UsageStatistics;
}

/**
 * Subscription Error Response
 */
export interface SubscriptionErrorResponse {
  success: false;
  message: string;
}

/**
 * Plan Comparison Data
 */
export interface PlanComparison {
  currentPlan: SubscriptionPlan;
  targetPlan: SubscriptionPlan;
  isUpgrade: boolean;
  isDowngrade: boolean;
  creditsDifference: number;
  queuesDifference: number;
  priceDifference: string;
}

/**
 * Subscription Alert Types
 */
export type SubscriptionAlertType = 
  | 'low_credits'
  | 'approaching_queue_limit'
  | 'subscription_expiring'
  | 'payment_failed'
  | 'upgrade_recommended';

/**
 * Subscription Alert
 */
export interface SubscriptionAlert {
  id: string;
  type: SubscriptionAlertType;
  title: string;
  message: string;
  severity: 'info' | 'warning' | 'error';
  actionLabel?: string;
  actionUrl?: string;
  dismissible: boolean;
  createdAt: string;
}

/**
 * Subscription Metrics
 */
export interface SubscriptionMetrics {
  totalCreditsUsed: number;
  totalCreditsRemaining: number;
  averageCreditsPerDay: number;
  projectedCreditsNeeded: number;
  queuesUtilization: number;
  costPerCredit: number;
  monthlyValue: number;
}

/**
 * Billing History Item
 */
export interface BillingHistoryItem {
  id: string;
  date: string;
  description: string;
  amount: string;
  status: 'paid' | 'pending' | 'failed';
  invoiceUrl?: string;
}

/**
 * Subscription Filters for API calls
 */
export interface SubscriptionFilters {
  period?: UsagePeriod;
  includeHistory?: boolean;
  includeProjections?: boolean;
}

/**
 * Plan Feature Comparison
 */
export interface PlanFeature {
  name: string;
  included: boolean;
  value?: string | number;
  highlight?: boolean;
}

/**
 * Enhanced Plan for Comparison
 */
export interface EnhancedSubscriptionPlan extends SubscriptionPlan {
  isCurrentPlan?: boolean;
  isRecommended?: boolean;
  savings?: string;
  features: PlanFeature[];
  monthlyCredits?: number;
  yearlyDiscount?: string;
}

/**
 * Payment Processor Types
 */
export type PaymentProcessor = 'lemonsqueezy' | 'chargily';

/**
 * Payment Method Types
 */
export type PaymentMethodType = 'card' | 'paypal' | 'edahabia' | 'cib';

/**
 * Payment Method
 */
export interface PaymentMethod {
  id: string;
  name: string;
  displayName: string;
  description: string;
  supportedMethods: PaymentMethodType[];
  currency: string;
  isRecommended: boolean;
  isAvailable: boolean;
  region?: string[];
  features: string[];
}

/**
 * Payment Methods Response
 */
export interface PaymentMethodsResponse {
  success: boolean;
  message: string;
  data: {
    methods: PaymentMethod[];
    recommendedMethod: string;
    providerCountry: string;
  };
}



/**
 * Chargily Checkout Response
 */
export interface ChargilyCheckoutResponse {
  success: boolean;
  message: string;
  data: {
    sessionId: string;
    checkoutUrl: string;
    planId: string;
    planName: string;
    amount: number;
    currency: string;
    paymentMethod: string;
    paymentProcessor: string;
    expiresAt: string;
    metadata: Record<string, any>;
  };
}

/**
 * Chargily Payment Status
 */
export interface ChargilyPaymentStatus {
  provider: {
    id: string;
    businessName: string;
    subscriptionPlan: string;
    subscriptionStatus: SubscriptionStatus;
    paymentProcessor: PaymentProcessor;
    paymentMethod: PaymentMethodType;
    credits: number;
    nextBillingDate: string;
    billingCycle: 'monthly' | 'yearly';
  };
  currentPlan: {
    id: string;
    name: string;
    price: number;
    currency: string;
    features: string[];
  };
  paymentHistory: ChargilyPaymentHistoryItem[];
  usage: {
    appointmentsThisMonth: number;
    appointmentLimit: number;
    creditsUsed: number;
    creditsRemaining: number;
  };
}

/**
 * Chargily Payment History Item
 */
export interface ChargilyPaymentHistoryItem {
  id: string;
  amount: number;
  currency: string;
  status: 'paid' | 'pending' | 'failed';
  paymentMethod: PaymentMethodType;
  description: string;
  paidAt: string;
  invoiceUrl?: string;
}

/**
 * Chargily Payment Status Response
 */
export interface ChargilyPaymentStatusResponse {
  success: boolean;
  message: string;
  data: ChargilyPaymentStatus;
}
