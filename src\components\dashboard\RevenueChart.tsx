import React, { useState } from 'react';
import { useRevenueChart } from '../../hooks/useDashboard';

const RevenueChart: React.FC = () => {
  const [period, setPeriod] = useState<'week' | 'month'>('week');
  const { data: chartData, isLoading, error } = useRevenueChart(period);

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 animate-pulse">
        <div className="flex items-center justify-between mb-6">
          <div>
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-32 mb-2"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-48"></div>
          </div>
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
        </div>
        <div className="h-48 bg-gray-200 dark:bg-gray-700 rounded"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Error Loading Chart
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Unable to load revenue chart data. Please try again.
          </p>
        </div>
      </div>
    );
  }

  if (!chartData) {
    return null;
  }

  const maxValue = Math.max(...chartData.data);
  const totalRevenue = chartData.data.reduce((sum, value) => sum + value, 0);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Revenue Overview
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            ${totalRevenue.toLocaleString()} total revenue
          </p>
        </div>
        <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          <button
            onClick={() => setPeriod('week')}
            className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
              period === 'week'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            Week
          </button>
          <button
            onClick={() => setPeriod('month')}
            className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
              period === 'month'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            Month
          </button>
        </div>
      </div>

      {/* Simple Bar Chart */}
      <div className="space-y-4">
        <div className="flex items-end justify-between h-48 gap-2">
          {chartData.data.map((value, index) => {
            const height = (value / maxValue) * 100;
            return (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div className="w-full flex flex-col items-center">
                  {/* Bar */}
                  <div
                    className="w-full bg-gradient-to-t from-brand-500 to-brand-400 rounded-t-md transition-all duration-300 hover:from-brand-600 hover:to-brand-500 cursor-pointer relative group"
                    style={{ height: `${height}%` }}
                  >
                    {/* Tooltip */}
                    <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                      ${value.toLocaleString()}
                    </div>
                  </div>
                  
                  {/* Label */}
                  <div className="mt-2 text-xs text-gray-600 dark:text-gray-400 text-center">
                    {chartData.labels[index]}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Legend */}
        <div className="flex items-center justify-center gap-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-gradient-to-r from-brand-500 to-brand-400 rounded"></div>
            <span className="text-sm text-gray-600 dark:text-gray-400">Daily Revenue</span>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-3 gap-4 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <div className="text-center">
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            ${(totalRevenue / chartData.data.length).toFixed(0)}
          </p>
          <p className="text-xs text-gray-600 dark:text-gray-400">Avg per day</p>
        </div>
        <div className="text-center">
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            ${Math.max(...chartData.data).toLocaleString()}
          </p>
          <p className="text-xs text-gray-600 dark:text-gray-400">Best day</p>
        </div>
        <div className="text-center">
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            ${Math.min(...chartData.data.filter(d => d > 0)).toLocaleString()}
          </p>
          <p className="text-xs text-gray-600 dark:text-gray-400">Lowest day</p>
        </div>
      </div>
    </div>
  );
};

export default RevenueChart;
