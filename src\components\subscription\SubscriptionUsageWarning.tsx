import React from 'react';
import { useAuth } from '../../context/AuthContext';
import { useNavigate } from 'react-router';
import Button from '../ui/button/Button';

interface SubscriptionUsageWarningProps {
  className?: string;
  showOnlyWarnings?: boolean;
  creditThreshold?: number;
  queueThreshold?: number;
}

export default function SubscriptionUsageWarning({
  className = '',
  showOnlyWarnings = false,
  creditThreshold = 0.2, // Show warning when 20% or less credits remain
  queueThreshold = 0.9, // Show warning when 90% or more queues are used
}: SubscriptionUsageWarningProps) {
  const navigate = useNavigate();
  const { 
    subscription, 
    subscriptionLoading, 
    hasActiveSubscription,
    getCurrentCredits,
    getCurrentQueueLimit,
    getTotalAvailableCredits,
    getCurrentQueueUsed,
    isAtCreditLimit,
    isAtQueueLimit
  } = useAuth();

  if (subscriptionLoading || !subscription) {
    return null;
  }

  const currentCredits = getCurrentCredits();
  const totalAvailableCredits = getTotalAvailableCredits();
  const currentQueues = getCurrentQueueUsed();
  const maxCredits = totalAvailableCredits;
  const maxQueues = getCurrentQueueLimit();

  const creditsPercentage = maxCredits > 0 ? ( 1 - currentCredits / totalAvailableCredits )  : 0;
  const queuesPercentage = maxQueues > 0 ? currentQueues / maxQueues : 0;

  const isLowCredits = creditsPercentage <= creditThreshold;
  const isHighQueueUsage = queuesPercentage >= queueThreshold;
  const isAtCreditsLimit = isAtCreditLimit(0.1);
  const isAtQueuesLimit = isAtQueueLimit();
  console.log("usage warnings " , {
    isLowCredits,
    isHighQueueUsage,
    isAtCreditsLimit,
    isAtQueuesLimit,
  });
  console.log("usage warnings data ", {
    currentCredits,
    totalAvailableCredits,
    currentQueues,
    maxCredits,
    maxQueues,
    creditsPercentage,
    queuesPercentage,
  });
  // Don't show anything if no warnings and showOnlyWarnings is true
  if (showOnlyWarnings && !isLowCredits && !isHighQueueUsage && !isAtCreditsLimit && !isAtQueuesLimit) {
    return null;
  }

  const getWarningType = () => {
    if (isAtCreditsLimit || isAtQueuesLimit) return 'error';
    if (isLowCredits || isHighQueueUsage) return 'warning';
    return 'info';
  };

  const getWarningIcon = (type: string) => {
    switch (type) {
      case 'error':
        return (
          <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return (
          <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  const getWarningMessage = () => {
    const messages = [];

    if (isAtCreditsLimit) {
      messages.push(`You're running very low on credits (${currentCredits} remaining)`);
    } else if (isLowCredits) {
      messages.push(`You're running low on credits (${currentCredits}/${maxCredits})`);
    }

    if (isAtQueuesLimit) {
      messages.push(`You've reached your queue limit (${currentQueues}/${maxQueues})`);
    } else if (isHighQueueUsage) {
      messages.push(`You're using most of your queues (${currentQueues}/${maxQueues})`);
    }

    if (messages.length === 0) {
      return `You're on the ${subscription.subscription.planName} plan with ${currentCredits} credits and ${currentQueues}/${maxQueues} queues used.`;
    }

    return messages.join(' and ');
  };

  const getActionText = () => {
    if (isAtCreditsLimit || isAtQueuesLimit) {
      return 'Upgrade Now';
    }
    if (isLowCredits || isHighQueueUsage) {
      return 'View Plans';
    }
    return 'Manage Subscription';
  };

  const getBackgroundColor = (type: string) => {
    switch (type) {
      case 'error':
        return 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800';
      default:
        return 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800';
    }
  };

  const getTextColor = (type: string) => {
    switch (type) {
      case 'error':
        return 'text-red-800 dark:text-red-200';
      case 'warning':
        return 'text-yellow-800 dark:text-yellow-200';
      default:
        return 'text-blue-800 dark:text-blue-200';
    }
  };

  const warningType = getWarningType();

  return (
    <div className={`rounded-lg border p-4 ${getBackgroundColor(warningType)} ${className}`}>
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          {getWarningIcon(warningType)}
        </div>
        
        <div className="flex-1 min-w-0">
          <h4 className={`text-sm font-medium ${getTextColor(warningType)} mb-1`}>
            {warningType === 'error' ? 'Action Required' : 
             warningType === 'warning' ? 'Usage Warning' : 
             'Subscription Status'}
          </h4>
          
          <p className={`text-sm ${getTextColor(warningType)} mb-3`}>
            {getWarningMessage()}
          </p>

          {/* Usage Progress Bars */}
          <div className="space-y-2 mb-3">
            {/* Credits Progress */}
            <div>
              <div className="flex items-center justify-between text-xs mb-1">
                <span className={getTextColor(warningType)}>Credits</span>
                <span className={getTextColor(warningType)}>{currentCredits}/{maxCredits}</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    creditsPercentage <= 0.1 ? 'bg-red-500' :
                    creditsPercentage <= 0.2 ? 'bg-yellow-500' :
                    'bg-green-500'
                  }`}
                  style={{ width: `${Math.max(creditsPercentage * 100, 2)}%` }}
                ></div>
              </div>
            </div>

            {/* Queues Progress */}
            {maxQueues > 0 && (
              <div>
                <div className="flex items-center justify-between text-xs mb-1">
                  <span className={getTextColor(warningType)}>Queues</span>
                  <span className={getTextColor(warningType)}>{currentQueues}/{maxQueues}</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      queuesPercentage >= 1 ? 'bg-red-500' :
                      queuesPercentage >= 0.9 ? 'bg-yellow-500' :
                      'bg-green-500'
                    }`}
                    style={{ width: `${Math.min(queuesPercentage * 100, 100)}%` }}
                  ></div>
                </div>
              </div>
            )}
          </div>

          <div className="flex flex-col sm:flex-row gap-2">
            <Button
              onClick={() => navigate('/subscription')}
              variant={warningType === 'error' ? 'primary' : 'outline'}
              size="sm"
              className="text-xs"
            >
              {getActionText()}
            </Button>
            
            <Button
              onClick={() => navigate('/billing')}
              variant="outline"
              size="sm"
              className="text-xs"
            >
              View Usage
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Compact version for smaller spaces
interface CompactUsageWarningProps {
  className?: string;
}

export function CompactUsageWarning({ className = '' }: CompactUsageWarningProps) {
  return (
    <SubscriptionUsageWarning
      className={className}
      showOnlyWarnings={true}
      creditThreshold={0.1}
      queueThreshold={0.95}
    />
  );
}

// Banner version for top of pages
interface UsageWarningBannerProps {
  className?: string;
}

export function UsageWarningBanner({ className = '' }: UsageWarningBannerProps) {
  return (
    <SubscriptionUsageWarning
      className={`mb-6 ${className}`}
      showOnlyWarnings={true}
      creditThreshold={0.2}
      queueThreshold={0.8}
    />
  );
}
