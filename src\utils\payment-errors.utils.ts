/**
 * Payment Error Handling Utilities
 * Handles errors from different payment processors (LemonSqueezy, Chargily)
 */

export interface PaymentError {
  code: string;
  message: string;
  processor: 'lemonsqueezy' | 'chargily' | 'unknown';
  details?: any;
  userMessage: string;
}

/**
 * Chargily Pay specific error codes
 */
export const CHARGILY_ERROR_CODES = {
  INVALID_PAYMENT_METHOD: 'invalid_payment_method',
  INSUFFICIENT_FUNDS: 'insufficient_funds',
  CARD_DECLINED: 'card_declined',
  EXPIRED_CARD: 'expired_card',
  INVALID_CARD: 'invalid_card',
  PROCESSING_ERROR: 'processing_error',
  NETWORK_ERROR: 'network_error',
  AUTHENTICATION_FAILED: 'authentication_failed',
  PLAN_NOT_FOUND: 'plan_not_found',
  CURRENCY_NOT_SUPPORTED: 'currency_not_supported',
  REGION_NOT_SUPPORTED: 'region_not_supported',
} as const;

/**
 * LemonSqueezy specific error codes
 */
export const LEMONSQUEEZY_ERROR_CODES = {
  SUBSCRIPTION_NOT_FOUND: 'subscription_not_found',
  CUSTOMER_NOT_FOUND: 'customer_not_found',
  PAYMENT_FAILED: 'payment_failed',
  INVALID_PLAN: 'invalid_plan',
  CHECKOUT_EXPIRED: 'checkout_expired',
} as const;

/**
 * Parse and standardize Chargily Pay errors
 */
export function parseChargilyError(error: any): PaymentError {
  const defaultError: PaymentError = {
    code: 'unknown_error',
    message: 'An unexpected error occurred with Chargily Pay',
    processor: 'chargily',
    userMessage: 'Payment processing failed. Please try again or contact support.',
  };

  if (!error) return defaultError;

  // Handle HTTP errors
  if (error.response) {
    const { status, data } = error.response;
    
    switch (status) {
      case 400:
        return {
          code: data?.error_code || 'bad_request',
          message: data?.message || 'Invalid request to Chargily Pay',
          processor: 'chargily',
          details: data,
          userMessage: 'Invalid payment information. Please check your details and try again.',
        };
        
      case 401:
        return {
          code: CHARGILY_ERROR_CODES.AUTHENTICATION_FAILED,
          message: 'Authentication failed with Chargily Pay',
          processor: 'chargily',
          details: data,
          userMessage: 'Authentication error. Please refresh the page and try again.',
        };
        
      case 402:
        return {
          code: CHARGILY_ERROR_CODES.INSUFFICIENT_FUNDS,
          message: 'Insufficient funds for payment',
          processor: 'chargily',
          details: data,
          userMessage: 'Insufficient funds. Please check your account balance and try again.',
        };
        
      case 404:
        return {
          code: CHARGILY_ERROR_CODES.PLAN_NOT_FOUND,
          message: 'Subscription plan not found',
          processor: 'chargily',
          details: data,
          userMessage: 'The selected plan is no longer available. Please choose a different plan.',
        };
        
      case 422:
        return {
          code: CHARGILY_ERROR_CODES.INVALID_PAYMENT_METHOD,
          message: data?.message || 'Invalid payment method',
          processor: 'chargily',
          details: data,
          userMessage: 'Invalid payment method. Please check your payment details.',
        };
        
      case 500:
        return {
          code: CHARGILY_ERROR_CODES.PROCESSING_ERROR,
          message: 'Chargily Pay server error',
          processor: 'chargily',
          details: data,
          userMessage: 'Payment service is temporarily unavailable. Please try again later.',
        };
        
      default:
        return {
          ...defaultError,
          code: `http_${status}`,
          message: data?.message || `HTTP ${status} error from Chargily Pay`,
          details: data,
        };
    }
  }

  // Handle network errors
  if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
    return {
      code: CHARGILY_ERROR_CODES.NETWORK_ERROR,
      message: 'Network error connecting to Chargily Pay',
      processor: 'chargily',
      details: error,
      userMessage: 'Connection error. Please check your internet connection and try again.',
    };
  }

  // Handle timeout errors
  if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
    return {
      code: CHARGILY_ERROR_CODES.NETWORK_ERROR,
      message: 'Request timeout to Chargily Pay',
      processor: 'chargily',
      details: error,
      userMessage: 'Request timed out. Please try again.',
    };
  }

  return {
    ...defaultError,
    message: error.message || defaultError.message,
    details: error,
  };
}

/**
 * Parse and standardize LemonSqueezy errors
 */
export function parseLemonSqueezyError(error: any): PaymentError {
  const defaultError: PaymentError = {
    code: 'unknown_error',
    message: 'An unexpected error occurred with LemonSqueezy',
    processor: 'lemonsqueezy',
    userMessage: 'Payment processing failed. Please try again or contact support.',
  };

  if (!error) return defaultError;

  // Handle HTTP errors
  if (error.response) {
    const { status, data } = error.response;
    
    switch (status) {
      case 400:
        return {
          code: 'bad_request',
          message: data?.message || 'Invalid request to LemonSqueezy',
          processor: 'lemonsqueezy',
          details: data,
          userMessage: 'Invalid payment information. Please check your details and try again.',
        };
        
      case 404:
        return {
          code: LEMONSQUEEZY_ERROR_CODES.SUBSCRIPTION_NOT_FOUND,
          message: 'Subscription not found',
          processor: 'lemonsqueezy',
          details: data,
          userMessage: 'Subscription not found. Please contact support.',
        };
        
      default:
        return {
          ...defaultError,
          code: `http_${status}`,
          message: data?.message || `HTTP ${status} error from LemonSqueezy`,
          details: data,
        };
    }
  }

  return {
    ...defaultError,
    message: error.message || defaultError.message,
    details: error,
  };
}

/**
 * Generic payment error parser that routes to appropriate processor handler
 */
export function parsePaymentError(error: any, processor?: 'lemonsqueezy' | 'chargily'): PaymentError {
  // Try to detect processor from error context
  if (!processor) {
    if (error.response?.config?.url?.includes('chargily') || 
        error.response?.config?.url?.includes('providers/payment')) {
      processor = 'chargily';
    } else if (error.response?.config?.url?.includes('payment')) {
      processor = 'lemonsqueezy';
    }
  }

  switch (processor) {
    case 'chargily':
      return parseChargilyError(error);
    case 'lemonsqueezy':
      return parseLemonSqueezyError(error);
    default:
      return {
        code: 'unknown_processor',
        message: 'Unknown payment processor error',
        processor: 'unknown',
        userMessage: 'Payment processing failed. Please try again or contact support.',
        details: error,
      };
  }
}

/**
 * Log payment errors with appropriate context
 */
export function logPaymentError(error: PaymentError, context?: string): void {
  const logContext = context ? `[${context}]` : '[Payment Error]';
  
  console.error(`${logContext} ${error.processor.toUpperCase()} Error:`, {
    code: error.code,
    message: error.message,
    processor: error.processor,
    details: error.details,
    timestamp: new Date().toISOString(),
  });
}

/**
 * Check if error is retryable
 */
export function isRetryableError(error: PaymentError): boolean {
  const retryableCodes = [
    CHARGILY_ERROR_CODES.NETWORK_ERROR,
    CHARGILY_ERROR_CODES.PROCESSING_ERROR,
    'http_500',
    'http_502',
    'http_503',
    'http_504',
  ];
  
  return retryableCodes.includes(error.code);
}

/**
 * Get user-friendly error message for display
 */
export function getUserErrorMessage(error: PaymentError): string {
  return error.userMessage;
}
