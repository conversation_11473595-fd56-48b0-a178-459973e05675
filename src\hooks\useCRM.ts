import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { CRMService } from '../services/crm.service';
import {
  Customer,
  CustomerFolder,
  CustomerCommunication,
  CustomerCreateRequest,
  CustomerUpdateRequest,
  CustomerFolderUpdateRequest,
  CustomerCommunicationCreateRequest,
  CustomerFilters,
  CustomerStats,
  CustomerHistory,
  CustomerSegment,
  CustomerInsights,
} from '../types/customer';

// Query Keys
export const crmKeys = {
  all: ['crm'] as const,
  customers: () => [...crmKeys.all, 'customers'] as const,
  customer: (id: string) => [...crmKeys.customers(), id] as const,
  customerFolder: (id: string) => [...crmKeys.customer(id), 'folder'] as const,
  customerHistory: (id: string) => [...crmKeys.customer(id), 'history'] as const,
  customerCommunications: (id: string) => [...crmKeys.customer(id), 'communications'] as const,
  customerInsights: (id: string) => [...crmKeys.customer(id), 'insights'] as const,
  customerRecommendations: (id: string) => [...crmKeys.customer(id), 'recommendations'] as const,
  customerLoyalty: (id: string) => [...crmKeys.customer(id), 'loyalty'] as const,
  customersList: (filters?: CustomerFilters) => [...crmKeys.customers(), 'list', filters] as const,
  stats: () => [...crmKeys.all, 'stats'] as const,
  segments: () => [...crmKeys.all, 'segments'] as const,
  tags: () => [...crmKeys.all, 'tags'] as const,
};

/**
 * Hook for fetching customers with filters
 */
export const useCustomers = (filters?: CustomerFilters) => {
  return useQuery({
    queryKey: crmKeys.customersList(filters),
    queryFn: () => CRMService.getCustomers(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook for fetching a single customer
 */
export const useCustomer = (id: string) => {
  return useQuery({
    queryKey: crmKeys.customer(id),
    queryFn: () => CRMService.getCustomer(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

/**
 * Hook for fetching customer folder
 */
export const useCustomerFolder = (customerId: string) => {
  return useQuery({
    queryKey: crmKeys.customerFolder(customerId),
    queryFn: () => CRMService.getCustomerFolder(customerId),
    enabled: !!customerId,
    staleTime: 2 * 60 * 1000,
  });
};

/**
 * Hook for fetching customer history
 */
export const useCustomerHistory = (customerId: string) => {
  return useQuery({
    queryKey: crmKeys.customerHistory(customerId),
    queryFn: () => CRMService.getCustomerHistory(customerId),
    enabled: !!customerId,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

/**
 * Hook for fetching customer communications
 */
export const useCustomerCommunications = (customerId: string) => {
  return useQuery({
    queryKey: crmKeys.customerCommunications(customerId),
    queryFn: () => CRMService.getCustomerCommunications(customerId),
    enabled: !!customerId,
    staleTime: 30 * 1000, // 30 seconds
  });
};

/**
 * Hook for fetching customer insights
 */
export const useCustomerInsights = (customerId: string) => {
  return useQuery({
    queryKey: crmKeys.customerInsights(customerId),
    queryFn: () => CRMService.getCustomerInsights(customerId),
    enabled: !!customerId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook for fetching customer statistics
 */
export const useCustomerStats = () => {
  return useQuery({
    queryKey: crmKeys.stats(),
    queryFn: () => CRMService.getCustomerStats(),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

/**
 * Hook for fetching customer segments
 */
export const useCustomerSegments = () => {
  return useQuery({
    queryKey: crmKeys.segments(),
    queryFn: () => CRMService.getCustomerSegments(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook for fetching customer tags
 */
export const useCustomerTags = () => {
  return useQuery({
    queryKey: crmKeys.tags(),
    queryFn: () => CRMService.getCustomerTags(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook for creating a customer
 */
export const useCreateCustomer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CustomerCreateRequest) => CRMService.createCustomer(data),
    onSuccess: (newCustomer) => {
      // Invalidate customers list
      queryClient.invalidateQueries({ queryKey: crmKeys.customers() });
      queryClient.invalidateQueries({ queryKey: crmKeys.stats() });
      
      // Add to cache
      queryClient.setQueryData(crmKeys.customer(newCustomer.id), newCustomer);
      
      toast.success('Customer created successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to create customer');
    },
  });
};

/**
 * Hook for updating a customer
 */
export const useUpdateCustomer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CustomerUpdateRequest) => CRMService.updateCustomer(data),
    onSuccess: (updatedCustomer) => {
      // Update customer in cache
      queryClient.setQueryData(crmKeys.customer(updatedCustomer.id), updatedCustomer);
      
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: crmKeys.customers() });
      
      toast.success('Customer updated successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to update customer');
    },
  });
};

/**
 * Hook for updating customer folder
 */
export const useUpdateCustomerFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CustomerFolderUpdateRequest) => CRMService.updateCustomerFolder(data),
    onSuccess: (updatedFolder, variables) => {
      // Update folder in cache
      queryClient.setQueryData(crmKeys.customerFolder(variables.customerId), updatedFolder);
      
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: crmKeys.customer(variables.customerId) });
      queryClient.invalidateQueries({ queryKey: crmKeys.customers() });
      
      toast.success('Customer information updated successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to update customer information');
    },
  });
};

/**
 * Hook for adding customer communication
 */
export const useAddCustomerCommunication = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CustomerCommunicationCreateRequest) => CRMService.addCustomerCommunication(data),
    onSuccess: (newCommunication, variables) => {
      // Invalidate communications
      queryClient.invalidateQueries({ 
        queryKey: crmKeys.customerCommunications(variables.customerId) 
      });
      queryClient.invalidateQueries({ 
        queryKey: crmKeys.customerHistory(variables.customerId) 
      });
      
      toast.success('Communication added successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to add communication');
    },
  });
};

/**
 * Hook for adding customer tag
 */
export const useAddCustomerTag = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ customerId, tag }: { customerId: string; tag: string }) =>
      CRMService.addCustomerTag(customerId, tag),
    onSuccess: (_, { customerId }) => {
      // Invalidate customer data
      queryClient.invalidateQueries({ queryKey: crmKeys.customer(customerId) });
      queryClient.invalidateQueries({ queryKey: crmKeys.customerFolder(customerId) });
      queryClient.invalidateQueries({ queryKey: crmKeys.customers() });
      queryClient.invalidateQueries({ queryKey: crmKeys.tags() });
      
      toast.success('Tag added successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to add tag');
    },
  });
};

/**
 * Hook for removing customer tag
 */
export const useRemoveCustomerTag = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ customerId, tag }: { customerId: string; tag: string }) =>
      CRMService.removeCustomerTag(customerId, tag),
    onSuccess: (_, { customerId }) => {
      // Invalidate customer data
      queryClient.invalidateQueries({ queryKey: crmKeys.customer(customerId) });
      queryClient.invalidateQueries({ queryKey: crmKeys.customerFolder(customerId) });
      queryClient.invalidateQueries({ queryKey: crmKeys.customers() });
      
      toast.success('Tag removed successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to remove tag');
    },
  });
};

/**
 * Hook for adding loyalty points
 */
export const useAddLoyaltyPoints = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ customerId, points, reason }: { 
      customerId: string; 
      points: number; 
      reason: string; 
    }) => CRMService.addLoyaltyPoints(customerId, points, reason),
    onSuccess: (_, { customerId }) => {
      // Invalidate customer data
      queryClient.invalidateQueries({ queryKey: crmKeys.customer(customerId) });
      queryClient.invalidateQueries({ queryKey: crmKeys.customerLoyalty(customerId) });
      queryClient.invalidateQueries({ queryKey: crmKeys.customers() });
      
      toast.success('Loyalty points added successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to add loyalty points');
    },
  });
};

/**
 * Hook for searching customers
 */
export const useSearchCustomers = (query: string, filters?: CustomerFilters) => {
  return useQuery({
    queryKey: ['customer-search', query, filters],
    queryFn: () => CRMService.searchCustomers(query, filters),
    enabled: !!query && query.length >= 2,
    staleTime: 30 * 1000, // 30 seconds
  });
};
