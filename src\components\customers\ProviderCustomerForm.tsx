import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Button from '../ui/button/Button';
import Label from '../form/Label';
import Input from '../form/input/InputField';
import { ErrorDisplay } from '../error';
import { useCreateProviderCustomer, useUpdateProviderCustomer } from '../../hooks/useProviderCustomers';
import { ProviderCustomer, CustomerFormData } from '../../types/provider-customer';
import { XMarkIcon } from '../../icons';

// Validation schema based on documentation requirements
const customerSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  mobileNumber: z.string().min(1, 'Mobile number is required'),
  email: z.string().email('Invalid email format').optional().or(z.literal('')),
  nationalId: z.string().optional().or(z.literal('')),
  notes: z.string().max(1000, 'Notes cannot exceed 1000 characters').optional().or(z.literal('')),
});

interface ProviderCustomerFormProps {
  customer?: ProviderCustomer | null;
  onClose: () => void;
  onSuccess: () => void;
}

export default function ProviderCustomerForm({ customer, onClose, onSuccess }: ProviderCustomerFormProps) {
  const isEditing = !!customer;
  const createCustomerMutation = useCreateProviderCustomer();
  const updateCustomerMutation = useUpdateProviderCustomer();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<CustomerFormData>({
    resolver: zodResolver(customerSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      mobileNumber: '',
      email: '',
      nationalId: '',
      notes: '',
    },
  });

  // Populate form when editing
  useEffect(() => {
    if (customer) {
      reset({
        firstName: customer.firstName,
        lastName: customer.lastName,
        mobileNumber: customer.mobileNumber,
        email: customer.email || '',
        nationalId: customer.nationalId || '',
        notes: customer.notes || '',
      });
    }
  }, [customer, reset]);

  const onSubmit = async (data: CustomerFormData) => {
    try {
      // Clean up empty strings to undefined for optional fields
      const cleanData = {
        ...data,
        email: data.email?.trim() || undefined,
        nationalId: data.nationalId?.trim() || undefined,
        notes: data.notes?.trim() || undefined,
      };

      if (isEditing && customer) {
        await updateCustomerMutation.mutateAsync({
          customerUserId: customer.id,
          ...cleanData,
        });
      } else {
        await createCustomerMutation.mutateAsync(cleanData);
      }
      
      onSuccess();
    } catch (error) {
      // Error handling is done in the mutation hooks
    }
  };

  const isLoading = createCustomerMutation.isPending || updateCustomerMutation.isPending;
  const error = createCustomerMutation.error || updateCustomerMutation.error;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-3xl w-full">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {isEditing ? 'Edit Customer' : 'Add New Customer'}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {isEditing 
              ? 'Update customer information and provider notes'
              : 'Create a new customer relationship'
            }
          </p>
        </div>
        <button
          onClick={onClose}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
        >
          <XMarkIcon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
        {/* Error Display */}
        {error && (
          <ErrorDisplay
            error={error}
            title="Failed to save customer"
            variant="inline"
          />
        )}

        {/* Basic Information */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Basic Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="firstName" required>
                First Name
              </Label>
              <Input
                id="firstName"
                type="text"
                placeholder="Enter first name"
                error={errors.firstName?.message}
                {...register('firstName')}
              />
            </div>

            <div>
              <Label htmlFor="lastName" required>
                Last Name
              </Label>
              <Input
                id="lastName"
                type="text"
                placeholder="Enter last name"
                error={errors.lastName?.message}
                {...register('lastName')}
              />
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Contact Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <Label htmlFor="mobileNumber" required>
                Mobile Number
              </Label>
              <Input
                id="mobileNumber"
                type="tel"
                placeholder="Enter mobile number (e.g., +213555123456)"
                error={errors.mobileNumber?.message}
                {...register('mobileNumber')}
              />
            </div>

            <div>
              <Label htmlFor="email">
                Email Address
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter email address (optional)"
                error={errors.email?.message}
                {...register('email')}
              />
            </div>

            <div>
              <Label htmlFor="nationalId">
                National ID
              </Label>
              <Input
                id="nationalId"
                type="text"
                placeholder="Enter national ID (optional)"
                error={errors.nationalId?.message}
                {...register('nationalId')}
              />
            </div>
          </div>
        </div>

        {/* Provider Notes */}
        <div>
          <Label htmlFor="notes">
            Provider Notes
          </Label>
          <textarea
            id="notes"
            rows={4}
            placeholder="Add any notes about this customer (optional)"
            className="w-full rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm text-gray-800 placeholder:text-gray-400 focus:border-brand-300 focus:outline-none focus:ring-2 focus:ring-brand-500/10 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-500"
            {...register('notes')}
          />
          {errors.notes && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {errors.notes.message}
            </p>
          )}
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            Maximum 1000 characters
          </p>
        </div>

        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isLoading}
            loading={isLoading}
          >
            {isEditing ? 'Update Customer' : 'Create Customer'}
          </Button>
        </div>
      </form>
    </div>
  );
}
