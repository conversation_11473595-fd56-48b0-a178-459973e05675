import React, { useState } from 'react';
import { useQueueCapacity } from '../../hooks/useQueues';
import { QueueCapacity } from '../../types/queue';
import { CalenderIcon, TimeIcon, UserIcon, AlertIcon } from '../../icons';
import Button from '../ui/button/Button';
import Input from '../form/input/InputField';
import Label from '../form/Label';

interface QueueCapacityCalculatorProps {
  queueId: number;
  queueTitle: string;
  className?: string;
}

export default function QueueCapacityCalculator({ 
  queueId, 
  queueTitle, 
  className = '' 
}: QueueCapacityCalculatorProps) {
  const [selectedDate, setSelectedDate] = useState(
    new Date().toISOString().split('T')[0] // Today's date in YYYY-MM-DD format
  );

  const { 
    data: capacity, 
    isLoading, 
    error, 
    refetch 
  } = useQueueCapacity(queueId, selectedDate);

  const handleDateChange = (date: string) => {
    setSelectedDate(date);
  };

  const getCapacityStatus = (capacity: QueueCapacity) => {
    const utilizationRate = (capacity.existingAppointments / capacity.totalMinutes) * 100;
    
    if (utilizationRate >= 90) {
      return {
        status: 'critical',
        color: 'text-red-600 dark:text-red-400',
        bgColor: 'bg-red-50 dark:bg-red-900/20',
        borderColor: 'border-red-200 dark:border-red-800',
        message: 'Queue is at capacity'
      };
    } else if (utilizationRate >= 70) {
      return {
        status: 'warning',
        color: 'text-yellow-600 dark:text-yellow-400',
        bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
        borderColor: 'border-yellow-200 dark:border-yellow-800',
        message: 'Queue is getting busy'
      };
    } else {
      return {
        status: 'good',
        color: 'text-green-600 dark:text-green-400',
        bgColor: 'bg-green-50 dark:bg-green-900/20',
        borderColor: 'border-green-200 dark:border-green-800',
        message: 'Queue has good availability'
      };
    }
  };

  const formatMinutesToHours = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (hours === 0) {
      return `${remainingMinutes}m`;
    } else if (remainingMinutes === 0) {
      return `${hours}h`;
    } else {
      return `${hours}h ${remainingMinutes}m`;
    }
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Queue Capacity Calculator
        </h3>
        <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
          <CalenderIcon className="w-4 h-4" />
          <span>{queueTitle}</span>
        </div>
      </div>

      {/* Date Selection */}
      <div className="mb-6">
        <Label>Select Date</Label>
        <div className="flex items-center space-x-3">
          <Input
            type="date"
            value={selectedDate}
            onChange={(e) => handleDateChange(e.target.value)}
            min={new Date().toISOString().split('T')[0]}
            className="flex-1"
          />
          <Button
            onClick={() => refetch()}
            disabled={isLoading}
            size="sm"
            variant="outline"
          >
            Calculate
          </Button>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            Calculating capacity...
          </p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="flex items-center space-x-2 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <AlertIcon className="w-5 h-5 text-red-500" />
          <div>
            <p className="text-sm font-medium text-red-800 dark:text-red-200">
              Failed to calculate capacity
            </p>
            <p className="text-xs text-red-600 dark:text-red-300">
              {error?.message || 'Please try again later'}
            </p>
          </div>
        </div>
      )}

      {/* Capacity Results */}
      {capacity && !isLoading && (
        <div className="space-y-6">
          {/* Overall Status */}
          <div className={`p-4 rounded-lg border ${getCapacityStatus(capacity).bgColor} ${getCapacityStatus(capacity).borderColor}`}>
            <div className="flex items-center space-x-2 mb-2">
              <UserIcon className={`w-5 h-5 ${getCapacityStatus(capacity).color}`} />
              <span className={`font-medium ${getCapacityStatus(capacity).color}`}>
                {getCapacityStatus(capacity).message}
              </span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {capacity.existingAppointments} appointments scheduled out of {formatMinutesToHours(capacity.totalMinutes)} available
            </p>
          </div>

          {/* Capacity Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {formatMinutesToHours(capacity.totalMinutes)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Total Available
              </div>
            </div>

            <div className="text-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                {capacity.existingAppointments}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Appointments
              </div>
            </div>

            <div className="text-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {formatMinutesToHours(capacity.availableMinutes)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Available
              </div>
            </div>
          </div>

          {/* Service Breakdown */}
          {capacity.serviceCapacities && capacity.serviceCapacities.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                Service Breakdown
              </h4>
              <div className="space-y-3">
                {capacity.serviceCapacities.map((service) => (
                  <div key={service.serviceId} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                    <div>
                      <h5 className="font-medium text-gray-900 dark:text-white">
                        {service.serviceName}
                      </h5>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {service.duration} minutes per appointment
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-semibold text-gray-900 dark:text-white">
                        {service.maxAppointments}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        max appointments
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Utilization Bar */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Capacity Utilization
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {Math.round((capacity.existingAppointments / capacity.totalMinutes) * 100)}%
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
              <div
                className={`h-3 rounded-full transition-all duration-300 ${
                  (capacity.existingAppointments / capacity.totalMinutes) * 100 >= 90
                    ? 'bg-red-500'
                    : (capacity.existingAppointments / capacity.totalMinutes) * 100 >= 70
                    ? 'bg-yellow-500'
                    : 'bg-green-500'
                }`}
                style={{ 
                  width: `${Math.min((capacity.existingAppointments / capacity.totalMinutes) * 100, 100)}%` 
                }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
