/**
 * Profile completion components exports
 */

export { default as ProfileCompletionCard } from './ProfileCompletionCard';
export { default as ProfileCompletionExample } from './ProfileCompletionExample';
export { default as ProfileCompletionManager } from './ProfileCompletionManager';
export { default as ProfileCompletionDebug } from './ProfileCompletionDebug';
export { default as ProfilePageCompletionAlert } from './ProfilePageCompletionAlert';

// Re-export types for convenience
export type {
  ProfileCompletionCardProps,
  ProfileCompletionExampleProps,
  ProfileCompletionManagerProps,
  CompletionSection,
  CompletionAction,
} from '../../types/profile-completion';
