import React from 'react';
import Button from '../ui/button/Button';
import { Queue } from '../../types/queue';
import { useQueueStatus, useToggleQueueStatus } from '../../hooks/useQueues';

interface QueueCardProps {
  queue: Queue;
  onView: () => void;
  onEdit: () => void;
  onDelete: () => void;
}

export default function QueueCard({ queue, onView, onEdit, onDelete }: QueueCardProps) {
  const { data: queueStatus } = useQueueStatus(queue.id, queue.isActive);
  const toggleStatusMutation = useToggleQueueStatus();

  const handleToggleStatus = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await toggleStatusMutation.mutateAsync({
        id: queue.id,
        isActive: !queue.isActive
      });
    } catch (error) {
      // Error handled by mutation
    }
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive 
      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'low':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  const currentCount = queueStatus?.currentCapacity || 0;
  const maxCapacity = queue.maxCapacity || 0;
  const estimatedWait = queueStatus?.estimatedWaitTime || queue.estimatedWaitTime || 0;

  return (
    <div 
      className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow cursor-pointer"
      onClick={onView}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
            {queue.title}
          </h3>
          <div className="flex items-center space-x-2 mt-1">
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(queue.isActive)}`}>
              {queue.isActive ? 'Active' : 'Inactive'}
            </span>
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(queue.priority || 'medium')}`}>
              {queue.priority || 'Medium'} Priority
            </span>
          </div>
        </div>
        
        <div className="flex items-center space-x-2 ml-4">
          <button
            onClick={handleToggleStatus}
            disabled={toggleStatusMutation.isPending}
            className={`p-2 rounded-lg transition-colors ${
              queue.isActive 
                ? 'text-green-600 hover:bg-green-50 dark:text-green-400 dark:hover:bg-green-900/20'
                : 'text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'
            }`}
            title={queue.isActive ? 'Deactivate Queue' : 'Activate Queue'}
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>

      {/* Queue Metrics */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            {currentCount}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Current
          </div>
          {maxCapacity > 0 && (
            <div className="text-xs text-gray-400 dark:text-gray-500">
              / {maxCapacity} max
            </div>
          )}
        </div>
        
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            {estimatedWait}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Est. Wait (min)
          </div>
        </div>
      </div>

      {/* Capacity Bar */}
      {maxCapacity > 0 && (
        <div className="mb-4">
          <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
            <span>Capacity</span>
            <span>{Math.round((currentCount / maxCapacity) * 100)}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                currentCount / maxCapacity > 0.8 
                  ? 'bg-red-500' 
                  : currentCount / maxCapacity > 0.6 
                    ? 'bg-yellow-500' 
                    : 'bg-green-500'
              }`}
              style={{ width: `${Math.min((currentCount / maxCapacity) * 100, 100)}%` }}
            />
          </div>
        </div>
      )}

      {/* Services */}
      {queue.services && queue.services.length > 0 && (
        <div className="mb-4">
          <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
            Services ({queue.services.length})
          </div>
          <div className="flex flex-wrap gap-1">
            {queue.services.slice(0, 3).map((service, index) => (
              <span 
                key={index}
                className="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
              >
                Service {index + 1}
              </span>
            ))}
            {queue.services.length > 3 && (
              <span className="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400">
                +{queue.services.length - 3} more
              </span>
            )}
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex space-x-2">
          <Button
            onClick={(e) => {
              e.stopPropagation();
              onEdit();
            }}
            variant="outline"
            size="sm"
            className="text-xs"
          >
            Edit
          </Button>
          <Button
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            variant="outline"
            size="sm"
            className="text-red-600 border-red-300 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20 text-xs"
          >
            Delete
          </Button>
        </div>
        
        <div className="text-xs text-gray-500 dark:text-gray-400">
          Updated {new Date(queue.updatedAt).toLocaleDateString()}
        </div>
      </div>
    </div>
  );
}
