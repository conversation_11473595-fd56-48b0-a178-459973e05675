/**
 * Application configuration
 */

export const config = {
  // API Configuration
  api: {
    baseUrl: import.meta.env.VITE_NODE_ENV === 'production' 
      ? import.meta.env.VITE_API_BASE_URL_PROD 
      : import.meta.env.VITE_API_BASE_URL_DEV,
    timeout: 30000, // 30 seconds
    retryAttempts: 3,
    retryDelay: 1000, // 1 second
  },
  
  // App Configuration
  app: {
    name: import.meta.env.VITE_APP_NAME || 'Provider Dashboard',
    version: import.meta.env.VITE_APP_VERSION || '1.0.0',
    environment: import.meta.env.VITE_NODE_ENV || 'development',
  },
  
  // Debug Settings
  debug: {
    api: import.meta.env.VITE_DEBUG_API === 'true',
    auth: import.meta.env.VITE_DEBUG_AUTH === 'true',
  },
  
  // Authentication
  auth: {
    tokenKey: 'provider_auth_token',
    refreshTokenKey: 'provider_refresh_token',
    userKey: 'provider_user_data',
    sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
  },
  
  // API Endpoints
  endpoints: {
    auth: {
      requestEmailOtp: '/api/auth/request-email-otp',
      requestPhoneOtp: '/api/auth/request-otp',
      verifyOtpRegister: '/api/auth/provider/verify-otp-register',
      login: '/api/auth/provider/login',
      refreshToken: '/api/auth/refresh-token',
      passwordResetRequest: '/api/auth/request-password-reset-otp',
      passwordResetVerify: '/api/auth/verify-password-reset-otp',
      passwordReset: '/api/auth/reset-password',
    },
    user: {
      profilePicture: '/api/auth/user/profile-picture',
    },
    provider: {
      profile: '/api/auth/providers/profile',
      profileCompletion: '/api/auth/provider/profile-completion',
      logo: '/api/auth/provider/logo',
      logoMobile: '/api/auth/providers/mobile/logo',
    },
    services: {
      base: '/api/auth/providers/services',
      categories: '/api/auth/providers/service-categories',
    },
    locations: {
      base: '/api/auth/providers/locations',
    },
    schedules: {
      base: '/api/auth/providers/schedules',
    },
    appointments: {
      base: '/api/auth/providers/appointments',
      extend: '/api/auth/providers/time/appointments/extend',
    },
    queues: {
      base: '/api/auth/providers/queues',
      services: '/api/auth/providers/queue-services',
    },
    customers: {
      base: '/api/auth/providers/customers',
    },
    reschedules: {
      base: '/api/auth/providers/reschedules',
    },
    notifications: {
      base: '/api/auth/notifications',
      mobile: '/api/auth/notifications/mobile',
    },
    payment: {
      // Existing LemonSqueezy endpoints
      plans: '/api/auth/payment/plans',
      status: '/api/auth/payment/status',
      checkout: '/api/auth/payment/checkout',
      customerPortal: '/api/auth/payment/customer-portal',
      usage: '/api/auth/payment/usage',
      usedCredits: '/api/auth/payment/credits/used',

      // Chargily Pay endpoints (actual implemented endpoints)
      chargilyCustomer: '/api/auth/payment/chargily/customer',
      chargilyCheckout: '/api/auth/payment/chargily/checkout',
      chargilyStatus: '/api/auth/payment/chargily/status',
      chargilyPaymentLinks: '/api/auth/payment/chargily/payment-links',
      chargilyWebhook: '/api/payment/chargily/webhook',
    },
  },
} as const;

export type Config = typeof config;
