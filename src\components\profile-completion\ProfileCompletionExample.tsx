/**
 * ProfileCompletionExample Component
 * Complete implementation example showing integration with data fetching
 */

import React, { useEffect } from 'react';
import { useNavigate } from 'react-router';
import clsx from 'clsx';
import ProfileCompletionCard from './ProfileCompletionCard';
import { useProfileCompletion } from '../../hooks/useProfileCompletion';
import { useAuth } from '../../context/AuthContext';
import { ProfileCompletionExampleProps, CompletionSection, CompletionAction } from '../../types/profile-completion';

const ProfileCompletionExample: React.FC<ProfileCompletionExampleProps> = ({
  userId,
  showDetails = false,
  className,
  onDismiss,
}) => {
  const navigate = useNavigate();
  const { user, provider } = useAuth();
  const {
    completion,
    isLoading,
    error,
    refetch,
  } = useProfileCompletion();

  useEffect(() => {
    refetch();
  }, []);

  // Handle navigation to specific sections
  const handleActionClick = (section: CompletionSection, action: CompletionAction) => {
    const navigationMap: Record<CompletionSection, string> = {
      profilePicture: '/profile',
      providerInfo: '/profile',
      providingPlaces: '/locations',
      services: '/services',
      queues: '/queues',
    };

    const route = navigationMap[section];
    if (route) {
      navigate(route);
    }
  };

  // Handle card dismissal
  const handleDismiss = () => {
    // For now, just hide the card locally
    // In a real implementation, this could save to localStorage
    console.log('Card dismissed');
    if (onDismiss) {
      onDismiss();
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className={clsx('animate-pulse', className)}>
        <div className="bg-gray-200 rounded-lg h-48 mb-6"></div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={clsx(
        'bg-red-50 border border-red-200 rounded-lg p-6 mb-6',
        className
      )}>
        <div className="flex items-center">
          <span className="text-red-500 mr-2">⚠️</span>
          <div>
            <h3 className="text-red-800 font-medium">Failed to load profile completion</h3>
            <p className="text-red-600 text-sm mt-1">
              {error.message || 'An error occurred while fetching your profile completion status.'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Don't show card if not needed
  if (!completion || !user || !provider) {
    return null;
  }

  // Don't show if already 100% complete
  if (completion.overallPercentage >= 100) {
    return null;
  }

  // If we have completion data from API, use it directly
  // Otherwise, create profile completion data for calculation
  if (completion) {
    // API returned completion data, render the card with a mock data structure
    // since the card expects ProfileCompletionData but we have ProfileCompletionResult
    const mockData = {
      user,
      provider: {
        ...provider,
        category: provider.category || null,
        logo: provider.logo || null,
        providingPlaces: provider.providingPlaces || [],
        services: provider.services || [],
        queues: provider.queues || [],
      },
    };

    return (
      <div className={clsx('bg-white dark:bg-white/[0.03] rounded-2xl shadow-theme-sm border border-gray-200 dark:border-gray-800 p-6 mb-6', className)}>
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-brand-100 dark:bg-brand-500/15 rounded-full flex items-center justify-center">
              <span className="text-brand-600 dark:text-brand-400 text-lg">📊</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white/90">
                Profile Setup Progress
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {completion.overallCompleted ? 'Complete' : 'In Progress'} • {completion.overallPercentage}%
              </p>
            </div>
          </div>

          {onDismiss && (
            <button
              onClick={handleDismiss}
              className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"
              aria-label="Dismiss"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>

        {/* Overall Progress Bar */}
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Overall Progress</span>
            <span className="text-sm font-semibold text-gray-900 dark:text-white/90">
              {completion.overallPercentage}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3 dark:bg-gray-700">
            <div
              className={clsx(
                'h-3 rounded-full transition-all duration-300',
                completion.overallPercentage >= 100 ? 'bg-success-500' :
                completion.overallPercentage >= 80 ? 'bg-brand-500' :
                completion.overallPercentage >= 50 ? 'bg-warning-500' :
                completion.overallPercentage >= 25 ? 'bg-orange-500' : 'bg-error-500'
              )}
              style={{ width: `${completion.overallPercentage}%` }}
            />
          </div>
        </div>

        {/* Status Message */}
        {completion.overallCompleted ? (
          <div className="bg-success-50 border border-success-200 rounded-md p-3 mb-4 dark:bg-success-500/15 dark:border-success-500/30">
            <div className="flex items-center">
              <span className="text-success-500 mr-2">✅</span>
              <span className="text-success-800 dark:text-success-500 text-sm font-medium">
                Your profile is ready to accept appointments!
              </span>
            </div>
          </div>
        ) : (
          <div className="bg-brand-50 border border-brand-200 rounded-md p-3 mb-4 dark:bg-brand-500/15 dark:border-brand-500/30">
            <div className="flex items-center">
              <span className="text-brand-500 mr-2">ℹ️</span>
              <span className="text-brand-800 dark:text-brand-400 text-sm">
                Complete your profile to start accepting appointments
              </span>
            </div>
          </div>
        )}

        {/* Next Steps */}
        {completion.nextSteps.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Next Steps:</h4>
            <ul className="space-y-1">
              {completion.nextSteps.slice(0, 2).map((step, index) => (
                <li key={index} className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
                  <span className="text-brand-500 mr-2">•</span>
                  {step}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between items-center mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={() => {
              // Toggle details view or navigate to completion page
              console.log('View details clicked');
            }}
            className="text-brand-600 hover:text-brand-700 dark:text-brand-400 dark:hover:text-brand-300 text-sm font-medium transition-colors"
          >
            View Details
          </button>

          {!completion.overallCompleted && (
            <button
              onClick={() => {
                // Navigate to first incomplete section
                const firstIncompleteSection = Object.entries(completion.breakdown)
                  .find(([_, section]) => !section.completed)?.[0];
                if (firstIncompleteSection) {
                  handleActionClick(firstIncompleteSection as CompletionSection, 'complete');
                }
              }}
              className="px-4 py-2 bg-brand-500 text-white text-sm font-medium rounded-md hover:bg-brand-600 transition-colors shadow-theme-xs"
            >
              Continue Setup
            </button>
          )}
        </div>
      </div>
    );
  }

  return null;
};

export default ProfileCompletionExample;
