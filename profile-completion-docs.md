# Provider Profile Setup Completion Documentation

## Table of Contents
1. [Feature Overview](#feature-overview)
2. [Profile Completion Logic](#profile-completion-logic)
3. [API Endpoints](#api-endpoints)
4. [Database Schema](#database-schema)
5. [Frontend Components](#frontend-components)
6. [Business Rules](#business-rules)
7. [Integration Points](#integration-points)
8. [Code Examples](#code-examples)

## Feature Overview

### What is the Profile Completion System?
The Provider Profile Setup Completion feature is a comprehensive tracking system that monitors and guides providers through the onboarding process. It calculates completion percentages across different setup sections and provides actionable feedback to help providers complete their business setup.

### Why is it Important?
- **Guided Onboarding**: Provides clear direction for new providers
- **Quality Assurance**: Ensures providers have complete, professional profiles
- **User Experience**: Reduces abandonment during setup process
- **Business Intelligence**: Tracks onboarding funnel and completion rates
- **Platform Quality**: Maintains high standards for provider listings

### How it Tracks Setup Progress
The system monitors five key areas of provider setup:
1. **Profile Picture** (10% weight) - Business logo upload
2. **Provider Information** (30% weight) - Business details and category
3. **Providing Places** (25% weight) - Location setup with complete information
4. **Services** (20% weight) - Service offerings and pricing
5. **Queues** (15% weight) - Appointment scheduling resources

## Profile Completion Logic

### Completion Percentage Calculation
The system uses a weighted scoring algorithm where each section contributes to the overall completion percentage:

```typescript
const weights = {
  profilePicture: 10,    // Logo upload
  providerInfo: 30,      // Business information
  providingPlaces: 25,   // Locations
  services: 20,          // Service offerings
  queues: 15            // Scheduling queues
};

// Overall percentage = Σ(section_percentage × section_weight) / 100
```

### Section Tracking Details

#### 1. Profile Picture (10% weight)
- **Criteria**: Provider has uploaded a business logo
- **Completion**: Binary (0% or 100%)
- **Required**: `logoId` field is not null

#### 2. Provider Information (30% weight)
- **Required Fields**:
  - `title`: Business name/title
  - `phone`: Contact phone number
  - `presentation`: Business description
  - `category`: Provider category selection
- **Calculation**: `(completed_fields / total_fields) × 100`
- **Completion**: All 4 fields must be filled

#### 3. Providing Places (25% weight)
- **Criteria**: At least one location with complete information
- **Valid Location Requirements**:
  - `name`: Location name
  - `address`: Physical address
  - `city`: City information
- **Calculation**: `(valid_places / total_places) × 100`
- **Minimum Credit**: 20% if any locations exist

#### 4. Services (20% weight)
- **Criteria**: At least one service offering
- **Completion**: Binary (0% if no services, 100% if any services exist)
- **Required**: Minimum 1 service with complete information

#### 5. Queues (15% weight)
- **Criteria**: At least one active queue for appointment scheduling
- **Completion**: Binary (0% if no active queues, 100% if any active queues exist)
- **Required**: Minimum 1 queue with `isActive = true`

### Completion Thresholds
- **Setup Complete**: 80% overall completion (considered functional)
- **Perfect Setup**: 100% completion (all sections complete)
- **Auto-Mark Complete**: System automatically sets `isSetupComplete = true` when 100% reached

## API Endpoints

### Get Provider Profile Completion Status

**Endpoint:** `GET /api/auth/provider/profile-completion`

**Authentication:** Required (Bearer token)

**Description:** Retrieves the current completion status for the authenticated provider, including detailed breakdown and next steps.

#### Request
```http
GET /api/auth/provider/profile-completion
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

#### Response Format
```typescript
interface ProfileCompletionResponse {
  success: boolean;
  data: {
    overallPercentage: number;        // 0-100
    overallCompleted: boolean;        // true if >= 80%
    breakdown: {
      profilePicture: {
        completed: boolean;
        percentage: number;           // 0 or 100
        details: string;             // "Logo uploaded" or "No Logo uploaded"
      };
      providerInfo: {
        completed: boolean;
        percentage: number;           // 0-100 based on fields
        details: string;             // Missing fields description
        requiredFields: {
          title: boolean;
          phone: boolean;
          presentation: boolean;
          category: boolean;
        };
      };
      providingPlaces: {
        completed: boolean;
        percentage: number;           // Based on valid locations
        details: string;             // Location status description
        count: number;               // Total locations
        validPlaces: number;         // Locations with complete info
      };
      services: {
        completed: boolean;
        percentage: number;           // 0 or 100
        details: string;             // Service count description
        count: number;               // Total services
      };
      queues: {
        completed: boolean;
        percentage: number;           // 0 or 100
        details: string;             // Queue status description
        count: number;               // Total queues
        activeQueues: number;        // Active queue count
      };
    };
    nextSteps: string[];             // Actionable next steps
    criticalMissing: string[];       // Critical missing items
    shouldMarkAsComplete: boolean;   // Internal flag for auto-completion
  };
  message: string;
}
```

#### Success Response Example
```json
{
  "success": true,
  "data": {
    "overallPercentage": 85,
    "overallCompleted": true,
    "breakdown": {
      "profilePicture": {
        "completed": true,
        "percentage": 100,
        "details": "Logo uploaded"
      },
      "providerInfo": {
        "completed": true,
        "percentage": 100,
        "details": "All provider information completed",
        "requiredFields": {
          "title": true,
          "phone": true,
          "presentation": true,
          "category": true
        }
      },
      "providingPlaces": {
        "completed": true,
        "percentage": 100,
        "details": "All 2 location(s) have complete information",
        "count": 2,
        "validPlaces": 2
      },
      "services": {
        "completed": true,
        "percentage": 100,
        "details": "3 service(s) configured",
        "count": 3
      },
      "queues": {
        "completed": false,
        "percentage": 0,
        "details": "No active queues configured",
        "count": 0,
        "activeQueues": 0
      }
    },
    "nextSteps": [
      "Create at least one queue for appointment scheduling"
    ],
    "criticalMissing": [
      "Active queues for appointment scheduling"
    ],
    "shouldMarkAsComplete": false
  },
  "message": "Profile completion calculated successfully"
}
```

#### Error Responses

**401 Unauthorized:**
```json
{
  "message": "User not authenticated."
}
```

**404 Not Found:**
```json
{
  "success": false,
  "message": "Provider profile not found"
}
```

**500 Internal Server Error:**
```json
{
  "success": false,
  "message": "Failed to calculate profile completion",
  "error": "Detailed error information (development only)"
}
```

### Auto-Completion Behavior
When a provider reaches 100% completion, the API automatically:
1. Updates `SProvider.isSetupComplete = true`
2. Re-calculates completion with updated data
3. Returns the updated completion status

## Database Schema

### SProvider Model Fields
```sql
model SProvider {
  id              Int      @id @default(autoincrement())
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  -- Setup Completion Tracking
  isSetupComplete Boolean  @default(false)  -- Auto-updated when 100% complete
  isVerified      Boolean  @default(false)  -- Manual verification by platform
  
  -- Profile Information (30% weight)
  title           String?                   -- Business name/title
  phone           String?                   -- Contact phone
  presentation    String?  @db.Text        -- Business description
  category        ProviderCategory? @relation(fields: [providerCategoryId], references: [id])
  providerCategoryId Int?                   -- Business category
  
  -- Profile Picture (10% weight)
  logoId          String?                   -- Logo file reference
  logo            File?    @relation("ProviderLogo", fields: [logoId], references: [id])
  
  -- Related Entities (tracked for completion)
  providingPlaces SProvidingPlace[]         -- Locations (25% weight)
  services        Service[]                 -- Services (20% weight)
  queues          Queue[]                   -- Queues (15% weight)
  
  -- User Relationship
  user            User     @relation(fields: [userId], references: [id])
  userId          String   @unique
  
  -- Additional Fields
  serviceCategories ServiceCategory[]
  customerFolders   CustomerFolder[]
  averageRating     Float?
  totalReviews      Int      @default(0)
  reviewsReceived   Review[] @relation("ProviderReviews")
}
```

### Related Models for Completion Tracking

#### SProvidingPlace (Locations)
```sql
model SProvidingPlace {
  id          Int      @id @default(autoincrement())
  provider    SProvider @relation(fields: [sProviderId], references: [id])
  sProviderId Int
  
  -- Required for completion
  name        String   -- Location name
  address     String?  -- Physical address
  city        String?  -- City information
  
  -- Optional fields
  shortName   String?
  mobile      String?
  timezone    String?
  parking     Boolean  @default(false)
  elevator    Boolean  @default(false)
  handicapAccess Boolean @default(false)
}
```

#### Service (Services)
```sql
model Service {
  id          Int      @id @default(autoincrement())
  provider    SProvider @relation(fields: [sProviderId], references: [id])
  sProviderId Int
  
  -- Required fields
  title       String   -- Service name
  duration    Int      -- Duration in minutes
  pointsRequirements Int @default(1)
  
  -- Optional fields
  price       Float?
  isPublic    Boolean  @default(true)
  deliveryType String? -- "at_location", "at_customer", "both"
  description String?  @db.Text
}
```

#### Queue (Scheduling Resources)
```sql
model Queue {
  id          Int      @id @default(autoincrement())
  provider    SProvider? @relation(fields: [sProviderId], references: [id])
  sProviderId Int?
  
  title       String   -- Queue name
  isActive    Boolean  @default(true)  -- Required for completion
  
  sProvidingPlace   SProvidingPlace @relation(fields: [sProvidingPlaceId], references: [id])
  sProvidingPlaceId Int
}
```

## Frontend Components

### ProfileCompletionCard Component
**File:** `app/src/provider/components/ProfileCompletionCard.tsx`

**Purpose:** Main UI component that displays completion status with interactive progress tracking.

**Key Features:**
- Overall progress bar with color-coded status
- Expandable section breakdown
- Action buttons for each incomplete section
- Dismissible card with local storage persistence
- Critical missing items highlighting

**Props Interface:**
```typescript
interface ProfileCompletionCardProps {
  data: ProfileCompletionData;
  showDetails?: boolean;
  onActionClick?: (section: string, action: string) => void;
  onDismiss?: () => void;
  className?: string;
}
```

**Usage Example:**
```tsx
import ProfileCompletionCard from '@src/provider/components/ProfileCompletionCard';

<ProfileCompletionCard
  data={{ user, provider }}
  showDetails={true}
  onActionClick={(section, action) => {
    // Handle navigation to specific setup section
    console.log(`Navigate to ${section} for ${action}`);
  }}
/>
```

### ProfileCompletionExample Component
**File:** `app/src/provider/components/ProfileCompletionExample.tsx`

**Purpose:** Complete implementation example showing how to integrate the completion card with data fetching.

**Features:**
- Automatic data fetching with Wasp queries
- Loading and error states
- Real-time completion calculation
- Action handling for navigation

### ProfileCompletionManager Component
**File:** `app/src/provider/components/ProfileCompletionManager.tsx`

**Purpose:** Utility component for managing card visibility and testing.

**Features:**
- Reset dismissed card state
- Development/testing utilities
- Card visibility management

## Business Rules

### Setup Completion Criteria
A provider is considered "setup complete" when:

1. **Minimum Threshold**: Overall completion ≥ 80%
2. **Critical Sections**: Provider info, locations, and services must have some completion
3. **Auto-Completion**: System automatically marks `isSetupComplete = true` at 100%

### Required vs Optional Sections

#### Critical (Required for Basic Functionality)
- **Provider Information**: Business name, phone, description, category
- **Locations**: At least one location with name, address, city
- **Services**: At least one service offering

#### Important (Recommended)
- **Queues**: At least one active queue for scheduling
- **Profile Picture**: Business logo for professional appearance

### Completion Actions Required

#### To Reach 80% (Functional Setup)
1. Complete all provider information fields
2. Add at least one valid location
3. Create at least one service
4. Upload business logo

#### To Reach 100% (Perfect Setup)
1. All 80% requirements
2. Create at least one active queue
3. Ensure all locations have complete information

### Business Logic Rules
- **No Retroactive Penalties**: Existing incomplete data doesn't reduce percentages
- **Incremental Progress**: Each completed field immediately improves percentage
- **Weighted Importance**: Critical business info has higher weight than cosmetic elements
- **Minimum Viability**: 80% threshold ensures providers can accept appointments

## Integration Points

### Provider Onboarding Flow Integration
The completion system integrates with the multi-step onboarding process:

1. **Registration**: Creates provider with 0% completion
2. **Business Info Step**: Updates provider information (30% weight)
3. **Location Setup**: Adds providing places (25% weight)
4. **Service Configuration**: Creates service offerings (20% weight)
5. **Queue Setup**: Establishes scheduling resources (15% weight)
6. **Logo Upload**: Adds professional branding (10% weight)

### Mobile App Integration
The mobile app uses the completion API to:

**Dashboard Integration:**
```typescript
// Fetch completion status on dashboard load
const completionStatus = await fetch('/api/auth/provider/profile-completion', {
  headers: { Authorization: `Bearer ${token}` }
});

// Display progress widget
if (completionStatus.data.overallPercentage < 100) {
  showCompletionWidget(completionStatus.data);
}
```

**Onboarding Flow:**
```typescript
// Check completion after each step
const updateCompletion = async () => {
  const status = await getProfileCompletion();
  updateProgressBar(status.overallPercentage);
  
  if (status.overallCompleted) {
    showSuccessMessage("Setup complete! You can now accept appointments.");
  }
};
```

**Navigation Guidance:**
```typescript
// Use nextSteps for guided navigation
const nextSteps = completionStatus.data.nextSteps;
if (nextSteps.length > 0) {
  showNextStepPrompt(nextSteps[0]);
}
```

### Complete Setup API Integration
**Endpoint:** `POST /api/auth/provider/complete-setup`

This endpoint allows mobile apps to submit all onboarding data in a single request:

```typescript
const completeSetup = async (setupData: CompleteSetupRequest) => {
  const response = await fetch('/api/auth/provider/complete-setup', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(setupData)
  });
  
  if (response.ok) {
    // Setup completed - provider should now be at 100%
    const result = await response.json();
    console.log('Setup completed:', result.summary);
  }
};
```

## Code Examples

### TypeScript Interfaces

#### Core Data Structures
```typescript
// Main completion data interface
export interface ProfileCompletionData {
  user: User;
  provider: SProvider & {
    category?: ProviderCategory | null;
    providingPlaces?: (SProvidingPlace & {
      detailedAddress?: Address;
      queues?: Queue[];
    })[];
    services?: Service[];
    queues?: Queue[];
  };
}

// Detailed breakdown for each section
export interface CompletionBreakdown {
  profilePicture: SectionCompletion;
  providerInfo: ProviderInfoCompletion;
  providingPlaces: LocationCompletion;
  services: ServiceCompletion;
  queues: QueueCompletion;
}

// Section completion base interface
interface SectionCompletion {
  completed: boolean;
  percentage: number;
  details: string;
}

// Provider info specific completion
interface ProviderInfoCompletion extends SectionCompletion {
  requiredFields: {
    title: boolean;
    phone: boolean;
    presentation: boolean;
    category: boolean;
  };
}

// Final result interface
export interface ProfileCompletionResult {
  overallPercentage: number;
  overallCompleted: boolean;
  breakdown: CompletionBreakdown;
  nextSteps: string[];
  criticalMissing: string[];
  shouldMarkAsComplete: boolean;
}
```

#### API Request/Response Examples

**Get Completion Status:**
```typescript
// API call
const getProfileCompletion = async (): Promise<ProfileCompletionResult> => {
  const response = await fetch('/api/auth/provider/profile-completion', {
    headers: {
      'Authorization': `Bearer ${getAuthToken()}`,
      'Content-Type': 'application/json'
    }
  });
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
  
  const result = await response.json();
  return result.data;
};

// Usage in React component
const useProfileCompletion = () => {
  const [completion, setCompletion] = useState<ProfileCompletionResult | null>(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    getProfileCompletion()
      .then(setCompletion)
      .catch(console.error)
      .finally(() => setLoading(false));
  }, []);
  
  return { completion, loading };
};
```

### Usage Patterns

#### React Hook for Completion Tracking
```typescript
import { useQuery } from 'wasp/client/operations';
import { getUserServiceProvider } from 'wasp/client/operations';
import { calculateProfileCompletion } from '../utils/profileCompletion';

export const useProviderCompletion = (userId: string) => {
  const { data: provider, isLoading, error } = useQuery(
    getUserServiceProvider,
    { userId },
    { enabled: !!userId }
  );
  
  const completion = useMemo(() => {
    if (!provider) return null;
    return calculateProfileCompletion({
      user: { id: userId } as User,
      provider
    });
  }, [provider, userId]);
  
  return { completion, isLoading, error };
};
```

#### Mobile App Integration Example
```typescript
// Flutter/React Native service
class ProviderCompletionService {
  private baseUrl: string;
  private authToken: string;
  
  async getCompletionStatus(): Promise<ProfileCompletionResult> {
    const response = await fetch(`${this.baseUrl}/api/auth/provider/profile-completion`, {
      headers: {
        'Authorization': `Bearer ${this.authToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    const result = await response.json();
    if (!result.success) {
      throw new Error(result.message);
    }
    
    return result.data;
  }
  
  async checkAndUpdateCompletion(): Promise<boolean> {
    const status = await this.getCompletionStatus();
    
    // Update local state
    this.updateCompletionUI(status);
    
    // Return whether setup is complete
    return status.overallCompleted;
  }
  
  private updateCompletionUI(status: ProfileCompletionResult) {
    // Update progress bars, next steps, etc.
    this.updateProgressBar(status.overallPercentage);
    this.updateNextSteps(status.nextSteps);
    this.updateCriticalMissing(status.criticalMissing);
  }
}
```

#### Completion Calculation Utility
```typescript
// Core calculation function
export function calculateProfileCompletion(data: ProfileCompletionData): ProfileCompletionResult {
  const { user, provider } = data;

  // Safety guard for null/undefined data
  if (!user || !provider) {
    return createEmptyCompletionResult();
  }

  // Define section weights (total = 100)
  const weights = {
    profilePicture: 10,
    providerInfo: 30,
    providingPlaces: 25,
    services: 20,
    queues: 15
  };

  // Calculate each section
  const profilePictureResult = checkProfilePicture(provider);
  const providerInfoResult = checkProviderInfo(provider);
  const providingPlacesResult = checkProvidingPlaces(provider.providingPlaces || []);
  const servicesResult = checkServices(provider.services || []);
  const queuesResult = checkQueues(provider.queues || []);

  // Calculate weighted overall percentage
  const overallPercentage = Math.round(
    (profilePictureResult.percentage * weights.profilePicture +
     providerInfoResult.percentage * weights.providerInfo +
     providingPlacesResult.percentage * weights.providingPlaces +
     servicesResult.percentage * weights.services +
     queuesResult.percentage * weights.queues) / 100
  );

  // Determine completion status
  const overallCompleted = overallPercentage >= 80;
  const shouldMarkAsComplete = overallPercentage === 100 && !provider.isSetupComplete;

  return {
    overallPercentage,
    overallCompleted,
    breakdown: {
      profilePicture: profilePictureResult,
      providerInfo: providerInfoResult,
      providingPlaces: providingPlacesResult,
      services: servicesResult,
      queues: queuesResult
    },
    nextSteps: generateNextSteps(/* section results */),
    criticalMissing: generateCriticalMissing(/* section results */),
    shouldMarkAsComplete
  };
}
```

### Advanced Integration Examples

#### Dashboard Widget Implementation
```typescript
// Provider Dashboard Component
const ProviderDashboard: React.FC = () => {
  const { user } = useAuth();
  const { completion, isLoading } = useProviderCompletion(user.id);

  if (isLoading) return <Spin size="large" />;

  return (
    <div className="provider-dashboard">
      {/* Show completion card if not 100% complete */}
      {completion && completion.overallPercentage < 100 && (
        <ProfileCompletionCard
          data={{ user, provider: completion.provider }}
          showDetails={false}
          onActionClick={handleCompletionAction}
          className="mb-6"
        />
      )}

      {/* Rest of dashboard content */}
      <DashboardStats />
      <RecentAppointments />
      <QuickActions />
    </div>
  );
};

const handleCompletionAction = (section: string, action: string) => {
  const navigationMap = {
    'profilePicture': '/provider/profile/logo',
    'providerInfo': '/provider/profile/edit',
    'providingPlaces': '/provider/locations',
    'services': '/provider/services',
    'queues': '/provider/queues'
  };

  const route = navigationMap[section];
  if (route) {
    navigate(route);
  }
};
```

#### Mobile App State Management
```typescript
// Redux/Context state management for mobile app
interface ProviderState {
  profile: ProviderProfile | null;
  completion: ProfileCompletionResult | null;
  isOnboardingComplete: boolean;
}

const providerSlice = createSlice({
  name: 'provider',
  initialState: {
    profile: null,
    completion: null,
    isOnboardingComplete: false
  } as ProviderState,
  reducers: {
    setCompletion: (state, action) => {
      state.completion = action.payload;
      state.isOnboardingComplete = action.payload.overallCompleted;
    },
    updateProfile: (state, action) => {
      state.profile = { ...state.profile, ...action.payload };
      // Trigger completion recalculation
    }
  }
});

// Async thunk for fetching completion
export const fetchProviderCompletion = createAsyncThunk(
  'provider/fetchCompletion',
  async (_, { getState }) => {
    const response = await providerApi.getCompletionStatus();
    return response.data;
  }
);
```

### Testing Examples

#### Unit Tests for Completion Logic
```typescript
// Jest tests for completion calculation
describe('calculateProfileCompletion', () => {
  const mockUser = { id: 'user-123' } as User;

  test('should return 0% for empty provider', () => {
    const mockProvider = {
      id: 1,
      userId: 'user-123',
      isSetupComplete: false,
      providingPlaces: [],
      services: [],
      queues: []
    } as SProvider;

    const result = calculateProfileCompletion({
      user: mockUser,
      provider: mockProvider
    });

    expect(result.overallPercentage).toBe(0);
    expect(result.overallCompleted).toBe(false);
  });

  test('should return 100% for complete provider', () => {
    const mockProvider = {
      id: 1,
      userId: 'user-123',
      title: 'Test Business',
      phone: '+123456789',
      presentation: 'Test description',
      providerCategoryId: 1,
      logoId: 'logo-123',
      isSetupComplete: false,
      providingPlaces: [{
        id: 1,
        name: 'Main Location',
        address: '123 Test St',
        city: 'Test City'
      }],
      services: [{
        id: 1,
        title: 'Test Service',
        duration: 30
      }],
      queues: [{
        id: 1,
        title: 'Test Queue',
        isActive: true
      }]
    } as SProvider;

    const result = calculateProfileCompletion({
      user: mockUser,
      provider: mockProvider
    });

    expect(result.overallPercentage).toBe(100);
    expect(result.overallCompleted).toBe(true);
    expect(result.shouldMarkAsComplete).toBe(true);
  });
});
```

#### API Integration Tests
```typescript
// API endpoint tests
describe('GET /api/auth/provider/profile-completion', () => {
  test('should return completion status for authenticated provider', async () => {
    const response = await request(app)
      .get('/api/auth/provider/profile-completion')
      .set('Authorization', `Bearer ${validToken}`)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data).toHaveProperty('overallPercentage');
    expect(response.body.data).toHaveProperty('breakdown');
    expect(response.body.data.breakdown).toHaveProperty('profilePicture');
    expect(response.body.data.breakdown).toHaveProperty('providerInfo');
  });

  test('should return 401 for unauthenticated request', async () => {
    await request(app)
      .get('/api/auth/provider/profile-completion')
      .expect(401);
  });
});
```

### Performance Considerations

#### Caching Strategy
```typescript
// Cache completion results to avoid recalculation
const completionCache = new Map<string, {
  result: ProfileCompletionResult;
  timestamp: number;
  ttl: number;
}>();

export const getCachedCompletion = (userId: string): ProfileCompletionResult | null => {
  const cached = completionCache.get(userId);
  if (cached && Date.now() - cached.timestamp < cached.ttl) {
    return cached.result;
  }
  return null;
};

export const setCachedCompletion = (
  userId: string,
  result: ProfileCompletionResult,
  ttl: number = 300000 // 5 minutes
) => {
  completionCache.set(userId, {
    result,
    timestamp: Date.now(),
    ttl
  });
};
```

#### Database Query Optimization
```typescript
// Optimized query with selective includes
const getProviderForCompletion = async (userId: string) => {
  return await prisma.sProvider.findUnique({
    where: { userId },
    include: {
      category: true,
      logo: true,
      providingPlaces: {
        select: {
          id: true,
          name: true,
          address: true,
          city: true,
          detailedAddress: true
        }
      },
      services: {
        select: {
          id: true,
          title: true,
          duration: true
        }
      },
      queues: {
        select: {
          id: true,
          title: true,
          isActive: true
        }
      }
    }
  });
};
```

### Error Handling Best Practices

#### Graceful Degradation
```typescript
// Handle partial data gracefully
export const calculateProfileCompletionSafe = (
  data: Partial<ProfileCompletionData>
): ProfileCompletionResult => {
  try {
    return calculateProfileCompletion(data as ProfileCompletionData);
  } catch (error) {
    console.error('Profile completion calculation failed:', error);

    // Return safe fallback
    return {
      overallPercentage: 0,
      overallCompleted: false,
      breakdown: createEmptyBreakdown(),
      nextSteps: ['Complete your provider profile setup'],
      criticalMissing: ['Unable to calculate completion status'],
      shouldMarkAsComplete: false
    };
  }
};
```

#### API Error Recovery
```typescript
// Retry logic for API calls
const getCompletionWithRetry = async (maxRetries = 3): Promise<ProfileCompletionResult> => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch('/api/auth/provider/profile-completion');
      if (response.ok) {
        const result = await response.json();
        return result.data;
      }
      throw new Error(`HTTP ${response.status}`);
    } catch (error) {
      if (attempt === maxRetries) {
        throw error;
      }
      // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
  throw new Error('Max retries exceeded');
};
```

---

*This comprehensive documentation covers all aspects of the Provider Profile Setup Completion feature, from basic concepts to advanced implementation patterns, providing developers with everything needed for successful integration.*
