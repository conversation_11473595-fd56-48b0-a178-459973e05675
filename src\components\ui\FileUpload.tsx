import React, { useState, useRef } from 'react';
import { ArrowUpIcon, CloseIcon, InfoErrorIcon, PlusIcon } from '../../icons';
import Button from './button/Button';
import { createFilePreview, formatFileSize, validateUploadFile } from '../../utils/s3-upload.utils';

export interface FileUploadProps {
  onFileSelect: (file: File) => void;
  onUpload?: () => void;
  onRemove?: () => void;
  currentImageUrl?: string | null;
  isUploading?: boolean;
  uploadProgress?: number;
  disabled?: boolean;
  accept?: string;
  maxSize?: number; // in bytes
  label?: string;
  description?: string;
  showPreview?: boolean;
  showUploadButton?: boolean;
  showRemoveButton?: boolean;
  className?: string;
  error?: string;
}

export default function FileUpload({
  onFileSelect,
  onUpload,
  onRemove,
  currentImageUrl,
  isUploading = false,
  uploadProgress = 0,
  disabled = false,
  accept = 'image/*',
  maxSize = 10 * 1024 * 1024, // 10MB default
  label = 'Upload Image',
  description = 'Choose an image file to upload',
  showPreview = true,
  showUploadButton = true,
  showRemoveButton = true,
  className = '',
  error,
}: FileUploadProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (file: File) => {
    // Validate file
    const validation = validateUploadFile(file);
    if (!validation.isValid) {
      setValidationError(validation.error || 'Invalid file');
      return;
    }

    setValidationError(null);
    setSelectedFile(file);
    
    // Create preview
    if (showPreview) {
      try {
        const preview = await createFilePreview(file);
        setPreviewUrl(preview);
      } catch (error) {
        console.error('Failed to create preview:', error);
      }
    }

    onFileSelect(file);
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setDragActive(false);
    
    const file = event.dataTransfer.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setDragActive(false);
  };

  const handleRemove = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
    setValidationError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    onRemove?.();
  };

  const handleUpload = () => {
    if (selectedFile && onUpload) {
      onUpload();
    }
  };

  const displayError = error || validationError;
  const displayImageUrl = previewUrl || currentImageUrl;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Label and Description */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          {label}
        </label>
        {description && (
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {description}
          </p>
        )}
      </div>

      {/* Upload Area */}
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-6 text-center transition-colors
          ${dragActive 
            ? 'border-brand-500 bg-brand-50 dark:bg-brand-900/20' 
            : 'border-gray-300 dark:border-gray-600'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:border-brand-400'}
          ${displayError ? 'border-red-300 bg-red-50 dark:bg-red-900/20' : ''}
        `}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => !disabled && fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          onChange={handleInputChange}
          disabled={disabled}
          className="hidden"
        />

        {isUploading ? (
          <div className="space-y-3">
            <div className="w-12 h-12 mx-auto bg-brand-100 dark:bg-brand-900/30 rounded-full flex items-center justify-center">
              <ArrowUpIcon className="w-6 h-6 text-brand-600 dark:text-brand-400 animate-pulse" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Uploading... {uploadProgress}%
              </p>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2">
                <div
                  className="bg-brand-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                />
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            <div className="w-12 h-12 mx-auto bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
              <ArrowUpIcon className="w-6 h-6 text-gray-400" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Click to upload or drag and drop
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                PNG, JPG up to {formatFileSize(maxSize)}
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Error Display */}
      {displayError && (
        <div className="flex items-center space-x-2 text-red-600 dark:text-red-400">
          <InfoErrorIcon className="w-4 h-4" />
          <span className="text-sm">{displayError}</span>
        </div>
      )}

      {/* Preview and Controls */}
      {(displayImageUrl || selectedFile) && (
        <div className="space-y-3">
          {/* Image Preview */}
          {showPreview && displayImageUrl && (
            <div className="relative inline-block">
              <img
                src={displayImageUrl}
                alt="Preview"
                className="w-24 h-24 object-cover rounded-lg border border-gray-200 dark:border-gray-700"
              />
              {showRemoveButton && (
                <button
                  onClick={handleRemove}
                  disabled={disabled || isUploading}
                  className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors disabled:opacity-50"
                >
                  <CloseIcon className="w-3 h-3" />
                </button>
              )}
            </div>
          )}

          {/* File Info */}
          {selectedFile && (
            <div className="text-sm text-gray-600 dark:text-gray-400">
              <p>Selected: {selectedFile.name}</p>
              <p>Size: {formatFileSize(selectedFile.size)}</p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-2">
            {showUploadButton && selectedFile && onUpload && (
              <Button
                onClick={handleUpload}
                disabled={disabled || isUploading || !!displayError}
                size="sm"
                className="flex items-center space-x-2"
              >
                <ArrowUpIcon className="w-4 h-4" />
                <span>Upload</span>
              </Button>
            )}
            
            {showRemoveButton && (currentImageUrl || selectedFile) && (
              <Button
                onClick={handleRemove}
                disabled={disabled || isUploading}
                variant="outline"
                size="sm"
                className="flex items-center space-x-2"
              >
                <CloseIcon className="w-4 h-4" />
                <span>Remove</span>
              </Button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
