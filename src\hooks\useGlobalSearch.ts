import { useState, useMemo, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router';

export interface SearchResult {
  id: string;
  title: string;
  description: string;
  path: string;
  category: 'page' | 'feature' | 'action';
  icon?: string;
  keywords?: string[];
}

// Define all searchable pages and features
const searchableItems: SearchResult[] = [
  // Main Pages
  {
    id: 'dashboard',
    title: 'Dashboard',
    description: 'Overview of your business metrics and activities',
    path: '/',
    category: 'page',
    icon: '📊',
    keywords: ['home', 'overview', 'metrics', 'analytics', 'stats']
  },
  {
    id: 'calendar',
    title: 'Calendar',
    description: 'View and manage appointments in calendar format',
    path: '/calendar',
    category: 'page',
    icon: '📅',
    keywords: ['schedule', 'appointments', 'booking', 'time', 'date']
  },
  {
    id: 'appointments',
    title: 'Appointments',
    description: 'Manage all your appointments and bookings',
    path: '/appointments',
    category: 'page',
    icon: '📋',
    keywords: ['bookings', 'schedule', 'clients', 'meetings']
  },
  {
    id: 'customers',
    title: 'Customers',
    description: 'Manage your customer database and relationships',
    path: '/customers',
    category: 'page',
    icon: '👥',
    keywords: ['clients', 'contacts', 'crm', 'people', 'database']
  },
  {
    id: 'services',
    title: 'Services',
    description: 'Configure your services, pricing, and offerings',
    path: '/services',
    category: 'page',
    icon: '⚙️',
    keywords: ['offerings', 'pricing', 'setup', 'configuration', 'products']
  },
  {
    id: 'locations',
    title: 'Locations',
    description: 'Manage your business locations and addresses',
    path: '/locations',
    category: 'page',
    icon: '📍',
    keywords: ['addresses', 'places', 'venues', 'setup', 'branches']
  },
  {
    id: 'queues',
    title: 'Queues',
    description: 'Manage waiting queues and walk-in customers',
    path: '/queues',
    category: 'page',
    icon: '🚶',
    keywords: ['waiting', 'walk-in', 'line', 'queue management']
  },
  {
    id: 'profile',
    title: 'Profile',
    description: 'Manage your provider profile and settings',
    path: '/profile',
    category: 'page',
    icon: '👤',
    keywords: ['account', 'settings', 'personal', 'information', 'details']
  },
  {
    id: 'advanced',
    title: 'Advanced Features',
    description: 'Access advanced tools and features',
    path: '/advanced',
    category: 'page',
    icon: '🔧',
    keywords: ['tools', 'advanced', 'features', 'settings']
  },

  // Quick Actions
  {
    id: 'new-appointment',
    title: 'New Appointment',
    description: 'Create a new appointment booking',
    path: '/calendar',
    category: 'action',
    icon: '➕',
    keywords: ['create', 'book', 'schedule', 'add', 'new booking']
  },
  {
    id: 'add-customer',
    title: 'Add Customer',
    description: 'Add a new customer to your database',
    path: '/customers',
    category: 'action',
    icon: '👤',
    keywords: ['create', 'new client', 'add contact', 'register']
  },
  {
    id: 'add-service',
    title: 'Add Service',
    description: 'Create a new service offering',
    path: '/services',
    category: 'action',
    icon: '⚙️',
    keywords: ['create', 'new service', 'offering', 'setup']
  },
  {
    id: 'add-location',
    title: 'Add Location',
    description: 'Add a new business location',
    path: '/locations',
    category: 'action',
    icon: '📍',
    keywords: ['create', 'new location', 'address', 'venue']
  },

  // Additional Features
  {
    id: 'settings',
    title: 'Settings',
    description: 'Configure your account and application settings',
    path: '/profile',
    category: 'feature',
    icon: '⚙️',
    keywords: ['configuration', 'preferences', 'account', 'setup']
  },
  {
    id: 'reports',
    title: 'Reports & Analytics',
    description: 'View business reports and analytics',
    path: '/',
    category: 'feature',
    icon: '📊',
    keywords: ['analytics', 'statistics', 'data', 'insights', 'metrics']
  },
  {
    id: 'help',
    title: 'Help & Support',
    description: 'Get help and support documentation',
    path: '/advanced',
    category: 'feature',
    icon: '❓',
    keywords: ['support', 'documentation', 'faq', 'assistance', 'guide']
  },
  {
    id: 'service-session',
    title: 'Service Session',
    description: 'Active appointment session management with timer',
    path: '/appointments', // Will redirect to specific session when appointment is started
    category: 'feature',
    icon: '⏱️',
    keywords: ['session', 'timer', 'active', 'in progress', 'current appointment']
  }
];

const RECENT_SEARCHES_KEY = 'dalti-recent-searches';
const MAX_RECENT_SEARCHES = 5;

export const useGlobalSearch = () => {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [recentSearches, setRecentSearches] = useState<SearchResult[]>([]);
  const navigate = useNavigate();

  // Load recent searches from localStorage on mount
  useEffect(() => {
    try {
      const saved = localStorage.getItem(RECENT_SEARCHES_KEY);
      if (saved) {
        const parsed = JSON.parse(saved);
        setRecentSearches(parsed);
      }
    } catch (error) {
      console.warn('Failed to load recent searches:', error);
    }
  }, []);

  // Save recent searches to localStorage
  const saveRecentSearches = useCallback((searches: SearchResult[]) => {
    try {
      localStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(searches));
      setRecentSearches(searches);
    } catch (error) {
      console.warn('Failed to save recent searches:', error);
    }
  }, []);

  // Add to recent searches
  const addToRecentSearches = useCallback((result: SearchResult) => {
    setRecentSearches(prev => {
      // Remove if already exists
      const filtered = prev.filter(item => item.id !== result.id);
      // Add to beginning
      const updated = [result, ...filtered].slice(0, MAX_RECENT_SEARCHES);
      saveRecentSearches(updated);
      return updated;
    });
  }, [saveRecentSearches]);

  // Filter and rank search results
  const searchResults = useMemo(() => {
    const searchTerm = query.toLowerCase().trim();

    // If no query, show recent searches
    if (!searchTerm) {
      return recentSearches;
    }

    return searchableItems
      .map(item => {
        let score = 0;

        // Exact title match gets highest score
        if (item.title.toLowerCase() === searchTerm) {
          score += 100;
        }
        // Title starts with search term
        else if (item.title.toLowerCase().startsWith(searchTerm)) {
          score += 80;
        }
        // Title contains search term
        else if (item.title.toLowerCase().includes(searchTerm)) {
          score += 60;
        }

        // Description contains search term
        if (item.description.toLowerCase().includes(searchTerm)) {
          score += 30;
        }

        // Keywords match
        if (item.keywords) {
          for (const keyword of item.keywords) {
            if (keyword.toLowerCase().includes(searchTerm)) {
              score += 20;
            }
            if (keyword.toLowerCase() === searchTerm) {
              score += 40;
            }
          }
        }

        return { ...item, score };
      })
      .filter(item => item.score > 0)
      .sort((a, b) => b.score - a.score)
      .slice(0, 8); // Limit to top 8 results
  }, [query, recentSearches]);

  const handleSearch = useCallback((searchQuery: string) => {
    setQuery(searchQuery);
    setIsOpen(searchQuery.length > 0);
  }, []);

  const handleSelectResult = useCallback((result: SearchResult) => {
    addToRecentSearches(result);
    navigate(result.path);
    setQuery('');
    setIsOpen(false);
  }, [navigate, addToRecentSearches]);

  const handleClose = useCallback(() => {
    setIsOpen(false);
  }, []);

  const clearSearch = useCallback(() => {
    setQuery('');
    setIsOpen(false);
  }, []);

  const clearRecentSearches = useCallback(() => {
    saveRecentSearches([]);
  }, [saveRecentSearches]);

  return {
    query,
    searchResults,
    isOpen,
    recentSearches,
    handleSearch,
    handleSelectResult,
    handleClose,
    clearSearch,
    clearRecentSearches
  };
};
